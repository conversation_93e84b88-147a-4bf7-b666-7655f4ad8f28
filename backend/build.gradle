plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.3'
    id 'io.spring.dependency-management' version '1.1.4'
}

group = 'com.redteamobile.talentsphere'
version = '0.0.1-SNAPSHOT'

java {
    sourceCompatibility = '21'
}

repositories {
    maven { url 'https://maven.aliyun.com/repository/public' }
    mavenCentral()
}

dependencies {
    // Spring Boot starters
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-mail'
    implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'

    // Database
    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.3'
    implementation 'mysql:mysql-connector-java:8.0.33'
    implementation 'org.mongodb:mongodb-driver-sync:4.11.1'

    implementation 'org.apache.commons:commons-lang3:3.12.0'

    // JWT
    implementation 'io.jsonwebtoken:jjwt-api:0.11.2'
    implementation 'io.jsonwebtoken:jjwt-impl:0.11.2'
    implementation 'io.jsonwebtoken:jjwt-jackson:0.11.2'

    // AspectJ
    implementation 'org.aspectj:aspectjrt:1.9.22'
    
    // File handling
    implementation 'commons-io:commons-io:2.11.0'
    implementation 'org.apache.tika:tika-core:2.7.0'
    implementation 'org.apache.pdfbox:pdfbox:2.0.27'
    implementation 'org.apache.poi:poi:5.2.3'
    implementation 'org.apache.poi:poi-ooxml:5.2.3'
    implementation 'org.apache.poi:poi-scratchpad:5.2.3'
    
    // NLP Processing
    implementation 'com.hankcs:hanlp:portable-1.8.4'
    
    // Lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    implementation 'com.google.api-client:google-api-client:2.7.1'
    implementation 'com.google.oauth-client:google-oauth-client-jetty:1.37.0'
    implementation 'com.google.apis:google-api-services-people:v1-rev20240313-2.0.0'
    
    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.mockito:mockito-core'
    testImplementation 'org.junit.jupiter:junit-jupiter-api'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    // Spring AOP and AspectJ dependencies
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.aspectj:aspectjweaver:1.9.19'
    implementation 'org.aspectj:aspectjrt:1.9.19'

    // HTML to Image
    implementation 'org.xhtmlrenderer:flying-saucer-core:9.1.22'
    implementation 'org.xhtmlrenderer:flying-saucer-pdf-openpdf:9.1.22'
    implementation 'com.itextpdf:itextpdf:5.5.13.3'
    
    // HTML解析和图像处理
    implementation 'org.jsoup:jsoup:1.16.1'
    implementation 'commons-io:commons-io:2.11.0'
    
    // HTTP Client
    implementation 'org.apache.httpcomponents:httpclient:4.5.14'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    
    // Redis
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.apache.commons:commons-pool2:2.11.1'

    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
}

tasks.named('test') {
    useJUnitPlatform()
} 