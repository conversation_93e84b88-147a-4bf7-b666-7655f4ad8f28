#!/usr/bin/env python3

import sys
import json
from pyresparser import ResumeParser
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def parse_resume(file_path):
    """
    Parse resume file and return structured data
    
    Args:
        file_path (str): Path to the resume file
        
    Returns:
        dict: Parsed resume data
    """
    try:
        logger.info(f"Parsing resume: {file_path}")
        data = ResumeParser(file_path).get_extracted_data()
        
        # Clean and structure the data
        structured_data = {
            "name": data.get("name", ""),
            "email": data.get("email", []),
            "phone_number": data.get("mobile_number", ""),
            "skills": data.get("skills", []),
            "experience": data.get("experience", []),
            "education": data.get("education", []),
            "total_experience": data.get("total_experience", 0)
        }
        
        # Convert to JSON string
        return json.dumps(structured_data)
        
    except Exception as e:
        logger.error(f"Error parsing resume: {str(e)}")
        return json.dumps({
            "error": str(e)
        })

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python resume_parser.py <file_path>")
        sys.exit(1)
        
    file_path = sys.argv[1]
    result = parse_resume(file_path)
    print(result)  # This will be captured by Java process 