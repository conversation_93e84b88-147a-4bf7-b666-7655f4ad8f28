package com.redteamobile.talentsphere.mapper;

import com.redteamobile.talentsphere.model.ResumeScreening;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface ResumeScreeningMapper {
    @Insert("INSERT INTO resume_screenings (resume_id, screening_result, matching_score, " +
            "screening_notes, screening_date, screened_by) VALUES " +
            "(#{resumeId}, #{screeningResult}, #{matchingScore}, #{screeningNotes}, " +
            "#{screeningDate}, #{screenedBy})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(ResumeScreening screening);

    @Select("SELECT * FROM resume_screenings WHERE resume_id = #{resumeId}")
    List<ResumeScreening> findByResumeId(Long resumeId);

    @Update("UPDATE resume_screenings SET screening_result = #{result}, " +
            "screening_notes = #{notes} WHERE id = #{id}")
    void updateResult(@Param("id") Long id, @Param("result") String result, 
                     @Param("notes") String notes);
} 