package com.redteamobile.talentsphere.mapper;

import com.redteamobile.talentsphere.model.InternalNotification;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface NotificationMapper {

    @Insert("INSERT INTO internal_notifications (sender, receiver, status, content, reference_type, reference_id) " +
            "VALUES (#{sender}, #{receiver}, #{status}, #{content}, #{referenceType}, #{referenceId})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(InternalNotification notification);

    @Select("SELECT * FROM internal_notifications WHERE id = #{id}")
    InternalNotification findById(Long id);

    @Select("SELECT * FROM internal_notifications WHERE receiver = #{receiver} " +
            "ORDER BY created_time DESC LIMIT #{offset}, #{limit}")
    List<InternalNotification> findByReceiver(
            @Param("receiver") String receiver,
            @Param("offset") int offset,
            @Param("limit") int limit);

    @Select("SELECT * FROM internal_notifications WHERE receiver = #{receiver} AND status = #{status} " +
            "ORDER BY created_time DESC LIMIT #{offset}, #{limit}")
    List<InternalNotification> findByReceiverAndStatus(
            @Param("receiver") String receiver,
            @Param("status") String status,
            @Param("offset") int offset,
            @Param("limit") int limit);

    @Select("SELECT COUNT(*) FROM internal_notifications WHERE receiver = #{receiver}")
    int countByReceiver(String receiver);

    @Select("SELECT COUNT(*) FROM internal_notifications WHERE receiver = #{receiver} AND status = #{status}")
    int countByReceiverAndStatus(
            @Param("receiver") String receiver,
            @Param("status") String status);

    @Update("UPDATE internal_notifications SET status = #{status}, updated_time = NOW() WHERE id = #{id}")
    int updateStatus(
            @Param("id") Long id,
            @Param("status") String status);

    @Update("UPDATE internal_notifications SET status = 'READ', updated_time = NOW() " +
            "WHERE receiver = #{receiver} AND status = 'CREATED'")
    int markAllAsRead(String receiver);

    @Delete("DELETE FROM internal_notifications WHERE id = #{id}")
    int delete(Long id);
} 