package com.redteamobile.talentsphere.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.redteamobile.talentsphere.model.StaffInfo;

@Mapper
public interface StaffInfoMapper {

    @Select("SELECT * FROM staff_info")
    List<StaffInfo> findAll();
    
    @Select("SELECT COUNT(*) FROM staff_info")
    int countAll();
    
    @Select("SELECT * FROM staff_info LIMIT #{offset}, #{limit}")
    List<StaffInfo> findAllPaginated(@Param("offset") int offset, @Param("limit") int limit);
    
    @Select("SELECT * FROM staff_info WHERE staff_id = #{staffId}")
    StaffInfo findByStaffId(String staffId);
    
    @Select("SELECT * FROM staff_info s WHERE MONTH(s.start_date) = MONTH(CURRENT_DATE) AND DAY(s.start_date) = DAY(CURRENT_DATE) AND s.employ_status = true")
    List<StaffInfo> findStaffWithAnniversaryToday();

    @Insert("INSERT INTO staff_info (staff_id, staff_name, email, gender, start_date, employ_status) " +
            "VALUES (#{staffId}, #{staffName}, #{email}, #{gender}, #{startDate}, #{employStatus}) " +
            "ON DUPLICATE KEY UPDATE staff_name = #{staffName}, email = #{email}, gender = #{gender}, start_date = #{startDate}, employ_status = #{employStatus}")
    void saveOrUpdate(StaffInfo staffInfo);
    
    @Delete("DELETE FROM staff_info WHERE staff_id = #{staffId}")
    void deleteByStaffId(String staffId);
} 