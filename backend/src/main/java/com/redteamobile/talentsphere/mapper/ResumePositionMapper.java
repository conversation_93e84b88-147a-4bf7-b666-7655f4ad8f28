package com.redteamobile.talentsphere.mapper;

import com.redteamobile.talentsphere.model.ResumePosition;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface ResumePositionMapper {
    
    @Select("SELECT rp.*, p.title as positionTitle " +
            "FROM resume_positions rp " +
            "JOIN positions p ON rp.position_id = p.id " +
            "WHERE rp.resume_id = #{resumeId}")
    @Results(id = "resumePositionMap", value = {
        @Result(property = "resumeId", column = "resume_id"),
        @Result(property = "positionId", column = "position_id"),
        @Result(property = "applicationDate", column = "application_date"),
        @Result(property = "status", column = "status"),
        @Result(property = "screeners", column = "screeners", typeHandler = com.redteamobile.talentsphere.typehandler.StringListTypeHandler.class),
        @Result(property = "positionTitle", column = "positionTitle")
    })
    List<ResumePosition> findByResumeId(@Param("resumeId") Long resumeId);
    
    @Insert("INSERT INTO resume_positions (resume_id, position_id, application_date, status, screeners) " +
            "VALUES (#{resumeId}, #{positionId}, NOW(), #{status}, #{screeners,typeHandler=com.redteamobile.talentsphere.typehandler.StringListTypeHandler}) " +
            "ON DUPLICATE KEY UPDATE " +
            "application_date = NOW(), " +
            "status = #{status}, " +
            "screeners = #{screeners,typeHandler=com.redteamobile.talentsphere.typehandler.StringListTypeHandler}")
    void insertOrUpdate(@Param("resumeId") Long resumeId,
                        @Param("positionId") Long positionId,
                        @Param("status") String status,
                        @Param("screeners") List<String> screeners);
    
    @Delete("DELETE FROM resume_positions WHERE resume_id = #{resumeId}")
    void deleteByResumeId(@Param("resumeId") Long resumeId);
    
    @Select("SELECT position_id FROM resume_positions WHERE resume_id = #{resumeId} LIMIT 1")
    Long findPositionIdByResumeId(@Param("resumeId") Long resumeId);
    
    @Update("UPDATE resume_positions SET status = 'TALENT_POOL' WHERE resume_id = #{resumeId}")
    void moveToTalentPool(@Param("resumeId") Long resumeId);
} 