package com.redteamobile.talentsphere.mapper;

import com.redteamobile.talentsphere.dto.UserSummary;
import com.redteamobile.talentsphere.model.User;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface UserMapper {
    @Insert("INSERT INTO users (username, password, email, role, dingtalk_userid) VALUES (#{username}, #{password}, #{email}, #{role}, #{dingtalkUserid})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(User user);

    @Select("SELECT id, username, password, email, role, enabled, dingtalk_userid as dingtalkUserid FROM users WHERE email = #{email}")
    User findByEmail(String email);

    @Update("UPDATE users SET password = #{password} WHERE id = #{id}")
    void updatePassword(User user);
    
    @Select("SELECT id, username, password, email, role, enabled, dingtalk_userid as dingtalkUserid FROM users WHERE role = #{role}")
    List<User> findByRole(String role);
    
    @Update("UPDATE users SET dingtalk_userid = #{dingtalkUserid} WHERE email = #{email}")
    void updateDingTalkUserId(@Param("email") String email, @Param("dingtalkUserid") String dingtalkUserid);
    
    @Select("SELECT email, username FROM users WHERE email LIKE CONCAT('%', #{query}, '%') AND enabled = true ORDER BY email LIMIT #{limit}")
    List<UserSummary> searchByEmail(@Param("query") String query, @Param("limit") int limit);
} 