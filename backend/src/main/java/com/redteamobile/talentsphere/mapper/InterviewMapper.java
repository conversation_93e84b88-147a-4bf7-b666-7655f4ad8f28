package com.redteamobile.talentsphere.mapper;

import com.redteamobile.talentsphere.model.Interview;
import com.redteamobile.talentsphere.dto.DailyCountRecord;
import com.redteamobile.talentsphere.dto.StatusCount;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

@Mapper
public interface InterviewMapper {

    @Insert("INSERT INTO interviews (resume_id, position_id, candidate, candidate_email, interviewer, contact, cc_list, " +
            "interview_type, scheduled_time, status, rating, feedback, qr_code_url, checkin_time, location, remark) " +
            "VALUES (#{resumeId}, #{positionId}, #{candidate}, #{candidateEmail}, #{interviewer}, #{contact}, #{ccList}, " +
            "#{interviewType}, #{scheduledTime}, #{status}, #{rating}, #{feedback}, #{qrCodeUrl}, " +
            "#{checkinTime}, #{location}, #{remark})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(Interview interview);

    @Select("SELECT * FROM interviews WHERE resume_id = #{resumeId}")
    @ResultMap("InterviewMap")
    List<Interview> findByResumeId(Long resumeId);

    @Select("SELECT * FROM interviews WHERE id = #{id}")
    @ResultMap("InterviewMap")
    Interview findById(Long id);

    @Update("UPDATE interviews SET status = #{status}, rating = #{rating}, feedback = #{feedback}, " +
            "result = #{result}, updated_at = NOW() WHERE id = #{id}")
    void updateInterviewFeedback(Interview interview);

    @Update("UPDATE interviews SET checkin_time = #{checkinTime}, status = 'IN_PROGRESS', updated_at = NOW() WHERE id = #{id}")
    void updateCheckinTime(@Param("id") Long id,
                           @Param("checkinTime") LocalDateTime checkinTime);

    @Update("UPDATE interviews SET " +
            "resume_id = #{resumeId}, " +
            "position_id = #{positionId}, " +
            "candidate = #{candidate}, " +
            "candidate_email = #{candidateEmail}, " +
            "interviewer = #{interviewer}, " +
            "contact = #{contact}, " +
            "cc_list = #{ccList}, " +
            "interview_type = #{interviewType}, " +
            "scheduled_time = #{scheduledTime}, " +
            "status = #{status}, " +
            "rating = #{rating}, " +
            "feedback = #{feedback}, " +
            "qr_code_url = #{qrCodeUrl}, " +
            "checkin_time = #{checkinTime}, " +
            "location = #{location}, " +
            "result = #{result}, " +
            "remark = #{remark}, " +
            "updated_at = #{updatedAt} " +
            "WHERE id = #{id}")
    void update(Interview interview);

    @Select("SELECT * FROM interviews " +
            "WHERE (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY scheduled_time DESC LIMIT #{offset}, #{pageSize}")
    @ResultMap("InterviewMap")
    List<Interview> findAllPaginated(@Param("offset") int offset,
                                     @Param("pageSize") int pageSize,
                                     @Param("search") String search);

    @Select("SELECT * FROM interviews " +
            "WHERE status = #{status} " +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY scheduled_time DESC LIMIT #{offset}, #{pageSize}")
    @ResultMap("InterviewMap")
    List<Interview> findByStatusPaginated(@Param("status") String status,
                                          @Param("offset") int offset,
                                          @Param("pageSize") int pageSize,
                                          @Param("search") String search);

    @Select("SELECT * FROM interviews " +
            "WHERE status = #{status} AND interviewer = #{interviewer} " +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY scheduled_time DESC LIMIT #{offset}, #{pageSize}")
    @ResultMap("InterviewMap")
    List<Interview> findByStatusAndInterviewerPaginated(@Param("status") String status,
                                                        @Param("interviewer") String interviewer,
                                                        @Param("offset") int offset,
                                                        @Param("pageSize") int pageSize,
                                                        @Param("search") String search);

    @Select("SELECT * FROM interviews " +
            "WHERE interviewer = #{interviewer} " +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY scheduled_time DESC LIMIT #{offset}, #{pageSize}")
    @ResultMap("InterviewMap")
    List<Interview> findByInterviewerPaginated(@Param("interviewer") String interviewer,
                                               @Param("offset") int offset,
                                               @Param("pageSize") int pageSize,
                                               @Param("search") String search);

    @Select("SELECT * FROM interviews WHERE status = #{status} ORDER BY scheduled_time DESC")
    @ResultMap("InterviewMap")
    List<Interview> findByStatus(String status);

    @Select("SELECT * FROM interviews ORDER BY scheduled_time DESC")
    @ResultMap("InterviewMap")
    List<Interview> findAll();

    @Select("SELECT * FROM interviews WHERE interviewer = #{interviewer} ORDER BY scheduled_time DESC")
    @ResultMap("InterviewMap")
    List<Interview> findByInterviewer(String email);

    @Select("SELECT * FROM interviews WHERE status = #{status} AND interviewer = #{interviewer} ORDER BY scheduled_time DESC")
    @ResultMap("InterviewMap")
    List<Interview> findByStatusAndInterviewer(String status, String interviewer);

    @Select("SELECT status, COUNT(*) as count FROM interviews GROUP BY status")
    @Results(id = "statusCountMap", value = {
        @Result(property = "status", column = "status"),
        @Result(property = "count", column = "count")
    })
    List<StatusCount> countByStatusForStatistic();

    @Select("SELECT COUNT(*) FROM interviews " +
            "WHERE status = #{status} " +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%'))")
    Long countByStatus(@Param("status") String status,
                      @Param("search") String search);

    @Select("SELECT COUNT(*) FROM interviews " +
            "WHERE (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%'))")
    Long countAll(@Param("search") String search);

    @Select("SELECT COUNT(*) FROM interviews " +
            "WHERE interviewer = #{interviewer} " +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%'))")
    Long countByInterviewer(@Param("interviewer") String interviewer,
                           @Param("search") String search);

    @Select("SELECT COUNT(*) FROM interviews " +
            "WHERE status = #{status} AND interviewer = #{interviewer} " +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%'))")
    Long countByStatusAndInterviewer(@Param("status") String status,
                                     @Param("interviewer") String interviewer,
                                     @Param("search") String search);

    @Select("SELECT DATE(scheduled_time) as date, COUNT(*) as count " +
            "FROM interviews " +
            "WHERE DATE(scheduled_time) BETWEEN #{startDate} AND #{endDate} " +
            "GROUP BY DATE(scheduled_time)")
    List<DailyCountRecord> countDailyInterviews(@Param("startDate") LocalDate startDate,
                                                @Param("endDate") LocalDate endDate);

    @Select("SELECT * FROM interviews " +
            "WHERE scheduled_time BETWEEN #{startDate} AND #{endDate} " +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY scheduled_time DESC LIMIT #{offset}, #{limit}")
    @ResultMap("InterviewMap")
    List<Interview> findByDateRangePaginated(
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("offset") int offset, 
        @Param("limit") int limit,
        @Param("search") String search);
        
    @Select("SELECT * FROM interviews " +
            "WHERE scheduled_time BETWEEN #{startDate} AND #{endDate} AND interviewer = #{interviewer} " +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY scheduled_time DESC LIMIT #{offset}, #{limit}")
    @ResultMap("InterviewMap")
    List<Interview> findByDateRangeAndInterviewerPaginated(
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("interviewer") String interviewer, 
        @Param("offset") int offset, 
        @Param("limit") int limit,
        @Param("search") String search);
        
    @Select("SELECT * FROM interviews " +
            "WHERE scheduled_time BETWEEN #{startDate} AND #{endDate} AND status = #{status} " +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY scheduled_time DESC LIMIT #{offset}, #{limit}")
    @ResultMap("InterviewMap")
    List<Interview> findByStatusAndDateRangePaginated(
        @Param("status") String status,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("offset") int offset, 
        @Param("limit") int limit,
        @Param("search") String search);
        
    @Select("SELECT * FROM interviews " +
            "WHERE scheduled_time BETWEEN #{startDate} AND #{endDate} AND status = #{status} AND interviewer = #{interviewer} " +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY scheduled_time DESC LIMIT #{offset}, #{limit}")
    @ResultMap("InterviewMap")
    List<Interview> findByStatusAndDateRangeAndInterviewerPaginated(
        @Param("status") String status,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("interviewer") String interviewer,
        @Param("offset") int offset, 
        @Param("limit") int limit,
        @Param("search") String search);
        
    @Select("SELECT COUNT(*) FROM interviews " +
            "WHERE scheduled_time BETWEEN #{startDate} AND #{endDate} " +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%'))")
    long countByDateRange(
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate,
        @Param("search") String search);
        
    @Select("SELECT COUNT(*) FROM interviews " +
            "WHERE scheduled_time BETWEEN #{startDate} AND #{endDate} AND interviewer = #{interviewer} " +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%'))")
    long countByDateRangeAndInterviewer(
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("interviewer") String interviewer,
        @Param("search") String search);
        
    @Select("SELECT COUNT(*) FROM interviews " +
            "WHERE scheduled_time BETWEEN #{startDate} AND #{endDate} AND status = #{status} " +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%'))")
    long countByStatusAndDateRange(
        @Param("status") String status,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate,
        @Param("search") String search);
        
    @Select("SELECT COUNT(*) FROM interviews " +
            "WHERE scheduled_time BETWEEN #{startDate} AND #{endDate} AND status = #{status} AND interviewer = #{interviewer} " +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%'))")
    long countByStatusAndDateRangeAndInterviewer(
        @Param("status") String status,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("interviewer") String interviewer,
        @Param("search") String search);

    @Select("SELECT * FROM interviews WHERE resume_id = #{resumeId} AND status = #{status} AND scheduled_time < #{currentTime} ORDER BY scheduled_time DESC")
    @ResultMap("InterviewMap")
    List<Interview> findCompletedInterviewsBeforeTime(
        @Param("resumeId") Long resumeId,
        @Param("status") String status,
        @Param("currentTime") LocalDateTime currentTime);

    // 按筛选者查询面试（分页）
    @Select("SELECT DISTINCT i.* FROM interviews i " +
            "JOIN resume_positions rp ON i.resume_id = rp.resume_id " +
            "WHERE FIND_IN_SET(#{screener}, rp.screeners) > 0 " +
            "AND (#{search} IS NULL OR i.candidate LIKE CONCAT('%', #{search}, '%') OR i.interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY i.scheduled_time DESC LIMIT #{offset}, #{pageSize}")
    @ResultMap("InterviewMap")
    List<Interview> findByScreenerPaginated(@Param("screener") String screener,
                                            @Param("offset") int offset,
                                            @Param("pageSize") int pageSize,
                                            @Param("search") String search);

    // 按状态和筛选者查询面试（分页）
    @Select("SELECT DISTINCT i.* FROM interviews i " +
            "JOIN resume_positions rp ON i.resume_id = rp.resume_id " +
            "WHERE i.status = #{status} AND FIND_IN_SET(#{screener}, rp.screeners) > 0 " +
            "AND (#{search} IS NULL OR i.candidate LIKE CONCAT('%', #{search}, '%') OR i.interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY i.scheduled_time DESC LIMIT #{offset}, #{pageSize}")
    @ResultMap("InterviewMap")
    List<Interview> findByStatusAndScreenerPaginated(@Param("status") String status,
                                                     @Param("screener") String screener,
                                                     @Param("offset") int offset,
                                                     @Param("pageSize") int pageSize,
                                                     @Param("search") String search);

    // 按日期范围和筛选者查询面试（分页）
    @Select("SELECT DISTINCT i.* FROM interviews i " +
            "JOIN resume_positions rp ON i.resume_id = rp.resume_id " +
            "WHERE i.scheduled_time BETWEEN #{startDate} AND #{endDate} " +
            "AND FIND_IN_SET(#{screener}, rp.screeners) > 0 " +
            "AND (#{search} IS NULL OR i.candidate LIKE CONCAT('%', #{search}, '%') OR i.interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY i.scheduled_time DESC LIMIT #{offset}, #{pageSize}")
    @ResultMap("InterviewMap")
    List<Interview> findByDateRangeAndScreenerPaginated(
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("screener") String screener,
        @Param("offset") int offset, 
        @Param("pageSize") int pageSize,
        @Param("search") String search);

    // 按状态、日期范围和筛选者查询面试（分页）
    @Select("SELECT DISTINCT i.* FROM interviews i " +
            "JOIN resume_positions rp ON i.resume_id = rp.resume_id " +
            "WHERE i.status = #{status} " +
            "AND i.scheduled_time BETWEEN #{startDate} AND #{endDate} " +
            "AND FIND_IN_SET(#{screener}, rp.screeners) > 0 " +
            "AND (#{search} IS NULL OR i.candidate LIKE CONCAT('%', #{search}, '%') OR i.interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY i.scheduled_time DESC LIMIT #{offset}, #{pageSize}")
    @ResultMap("InterviewMap")
    List<Interview> findByStatusAndDateRangeAndScreenerPaginated(
        @Param("status") String status,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("screener") String screener,
        @Param("offset") int offset, 
        @Param("pageSize") int pageSize,
        @Param("search") String search);

    // 计数方法
    @Select("SELECT COUNT(DISTINCT i.id) FROM interviews i " +
            "JOIN resume_positions rp ON i.resume_id = rp.resume_id " +
            "WHERE FIND_IN_SET(#{screener}, rp.screeners) > 0 " +
            "AND (#{search} IS NULL OR i.candidate LIKE CONCAT('%', #{search}, '%') OR i.interviewer LIKE CONCAT('%', #{search}, '%'))")
    long countByScreener(@Param("screener") String screener,
                        @Param("search") String search);

    @Select("SELECT COUNT(DISTINCT i.id) FROM interviews i " +
            "JOIN resume_positions rp ON i.resume_id = rp.resume_id " +
            "WHERE i.status = #{status} AND FIND_IN_SET(#{screener}, rp.screeners) > 0 " +
            "AND (#{search} IS NULL OR i.candidate LIKE CONCAT('%', #{search}, '%') OR i.interviewer LIKE CONCAT('%', #{search}, '%'))")
    long countByStatusAndScreener(@Param("status") String status,
                                  @Param("screener") String screener,
                                  @Param("search") String search);

    @Select("SELECT COUNT(DISTINCT i.id) FROM interviews i " +
            "JOIN resume_positions rp ON i.resume_id = rp.resume_id " +
            "WHERE i.scheduled_time BETWEEN #{startDate} AND #{endDate} " +
            "AND FIND_IN_SET(#{screener}, rp.screeners) > 0 " +
            "AND (#{search} IS NULL OR i.candidate LIKE CONCAT('%', #{search}, '%') OR i.interviewer LIKE CONCAT('%', #{search}, '%'))")
    long countByDateRangeAndScreener(
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("screener") String screener,
        @Param("search") String search);

    @Select("SELECT COUNT(DISTINCT i.id) FROM interviews i " +
            "JOIN resume_positions rp ON i.resume_id = rp.resume_id " +
            "WHERE i.status = #{status} " +
            "AND i.scheduled_time BETWEEN #{startDate} AND #{endDate} " +
            "AND FIND_IN_SET(#{screener}, rp.screeners) > 0 " +
            "AND (#{search} IS NULL OR i.candidate LIKE CONCAT('%', #{search}, '%') OR i.interviewer LIKE CONCAT('%', #{search}, '%'))")
    long countByStatusAndDateRangeAndScreener(
        @Param("status") String status,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("screener") String screener,
        @Param("search") String search);

    // 支持职位筛选的新方法
    @Select("<script>" +
            "SELECT * FROM interviews " +
            "WHERE scheduled_time BETWEEN #{startDate} AND #{endDate} " +
            "<if test='positionIds != null and positionIds.size() > 0'>" +
            "AND position_id IN " +
            "<foreach item='id' collection='positionIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "</if>" +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY scheduled_time DESC LIMIT #{offset}, #{limit}" +
            "</script>")
    @ResultMap("InterviewMap")
    List<Interview> findByDateRangeAndPositionsPaginated(
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("positionIds") List<Long> positionIds,
        @Param("offset") int offset, 
        @Param("limit") int limit,
        @Param("search") String search);

    @Select("<script>" +
            "SELECT COUNT(*) FROM interviews " +
            "WHERE scheduled_time BETWEEN #{startDate} AND #{endDate} " +
            "<if test='positionIds != null and positionIds.size() > 0'>" +
            "AND position_id IN " +
            "<foreach item='id' collection='positionIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "</if>" +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%'))" +
            "</script>")
    long countByDateRangeAndPositions(
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("positionIds") List<Long> positionIds,
        @Param("search") String search);

    @Select("<script>" +
            "SELECT * FROM interviews " +
            "WHERE scheduled_time BETWEEN #{startDate} AND #{endDate} AND status = #{status} " +
            "<if test='positionIds != null and positionIds.size() > 0'>" +
            "AND position_id IN " +
            "<foreach item='id' collection='positionIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "</if>" +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY scheduled_time DESC LIMIT #{offset}, #{limit}" +
            "</script>")
    @ResultMap("InterviewMap")
    List<Interview> findByStatusAndDateRangeAndPositionsPaginated(
        @Param("status") String status,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("positionIds") List<Long> positionIds,
        @Param("offset") int offset, 
        @Param("limit") int limit,
        @Param("search") String search);

    @Select("<script>" +
            "SELECT COUNT(*) FROM interviews " +
            "WHERE scheduled_time BETWEEN #{startDate} AND #{endDate} AND status = #{status} " +
            "<if test='positionIds != null and positionIds.size() > 0'>" +
            "AND position_id IN " +
            "<foreach item='id' collection='positionIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "</if>" +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%'))" +
            "</script>")
    long countByStatusAndDateRangeAndPositions(
        @Param("status") String status,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("positionIds") List<Long> positionIds,
        @Param("search") String search);

    // 支持职位筛选和面试官筛选的方法
    @Select("<script>" +
            "SELECT * FROM interviews " +
            "WHERE scheduled_time BETWEEN #{startDate} AND #{endDate} AND interviewer = #{interviewer} " +
            "<if test='positionIds != null and positionIds.size() > 0'>" +
            "AND position_id IN " +
            "<foreach item='id' collection='positionIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "</if>" +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY scheduled_time DESC LIMIT #{offset}, #{limit}" +
            "</script>")
    @ResultMap("InterviewMap")
    List<Interview> findByDateRangeAndPositionsAndInterviewerPaginated(
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("positionIds") List<Long> positionIds,
        @Param("interviewer") String interviewer,
        @Param("offset") int offset, 
        @Param("limit") int limit,
        @Param("search") String search);

    @Select("<script>" +
            "SELECT COUNT(*) FROM interviews " +
            "WHERE scheduled_time BETWEEN #{startDate} AND #{endDate} AND interviewer = #{interviewer} " +
            "<if test='positionIds != null and positionIds.size() > 0'>" +
            "AND position_id IN " +
            "<foreach item='id' collection='positionIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "</if>" +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%'))" +
            "</script>")
    long countByDateRangeAndPositionsAndInterviewer(
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("positionIds") List<Long> positionIds,
        @Param("interviewer") String interviewer,
        @Param("search") String search);

    @Select("<script>" +
            "SELECT * FROM interviews " +
            "WHERE scheduled_time BETWEEN #{startDate} AND #{endDate} AND status = #{status} AND interviewer = #{interviewer} " +
            "<if test='positionIds != null and positionIds.size() > 0'>" +
            "AND position_id IN " +
            "<foreach item='id' collection='positionIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "</if>" +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY scheduled_time DESC LIMIT #{offset}, #{limit}" +
            "</script>")
    @ResultMap("InterviewMap")
    List<Interview> findByStatusAndDateRangeAndPositionsAndInterviewerPaginated(
        @Param("status") String status,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("positionIds") List<Long> positionIds,
        @Param("interviewer") String interviewer,
        @Param("offset") int offset, 
        @Param("limit") int limit,
        @Param("search") String search);

    @Select("<script>" +
            "SELECT COUNT(*) FROM interviews " +
            "WHERE scheduled_time BETWEEN #{startDate} AND #{endDate} AND status = #{status} AND interviewer = #{interviewer} " +
            "<if test='positionIds != null and positionIds.size() > 0'>" +
            "AND position_id IN " +
            "<foreach item='id' collection='positionIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "</if>" +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%'))" +
            "</script>")
    long countByStatusAndDateRangeAndPositionsAndInterviewer(
        @Param("status") String status,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("positionIds") List<Long> positionIds,
        @Param("interviewer") String interviewer,
        @Param("search") String search);

    // 支持职位筛选和筛选者筛选的方法
    @Select("<script>" +
            "SELECT DISTINCT i.* FROM interviews i " +
            "JOIN resume_positions rp ON i.resume_id = rp.resume_id " +
            "WHERE i.scheduled_time BETWEEN #{startDate} AND #{endDate} " +
            "AND FIND_IN_SET(#{screener}, rp.screeners) > 0 " +
            "<if test='positionIds != null and positionIds.size() > 0'>" +
            "AND i.position_id IN " +
            "<foreach item='id' collection='positionIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "</if>" +
            "AND (#{search} IS NULL OR i.candidate LIKE CONCAT('%', #{search}, '%') OR i.interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY i.scheduled_time DESC LIMIT #{offset}, #{pageSize}" +
            "</script>")
    @ResultMap("InterviewMap")
    List<Interview> findByDateRangeAndPositionsAndScreenerPaginated(
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("positionIds") List<Long> positionIds,
        @Param("screener") String screener,
        @Param("offset") int offset, 
        @Param("pageSize") int pageSize,
        @Param("search") String search);

    @Select("<script>" +
            "SELECT COUNT(DISTINCT i.id) FROM interviews i " +
            "JOIN resume_positions rp ON i.resume_id = rp.resume_id " +
            "WHERE i.scheduled_time BETWEEN #{startDate} AND #{endDate} " +
            "AND FIND_IN_SET(#{screener}, rp.screeners) > 0 " +
            "<if test='positionIds != null and positionIds.size() > 0'>" +
            "AND i.position_id IN " +
            "<foreach item='id' collection='positionIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "</if>" +
            "AND (#{search} IS NULL OR i.candidate LIKE CONCAT('%', #{search}, '%') OR i.interviewer LIKE CONCAT('%', #{search}, '%'))" +
            "</script>")
    long countByDateRangeAndPositionsAndScreener(
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("positionIds") List<Long> positionIds,
        @Param("screener") String screener,
        @Param("search") String search);

    @Select("<script>" +
            "SELECT DISTINCT i.* FROM interviews i " +
            "JOIN resume_positions rp ON i.resume_id = rp.resume_id " +
            "WHERE i.status = #{status} " +
            "AND i.scheduled_time BETWEEN #{startDate} AND #{endDate} " +
            "AND FIND_IN_SET(#{screener}, rp.screeners) > 0 " +
            "<if test='positionIds != null and positionIds.size() > 0'>" +
            "AND i.position_id IN " +
            "<foreach item='id' collection='positionIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "</if>" +
            "AND (#{search} IS NULL OR i.candidate LIKE CONCAT('%', #{search}, '%') OR i.interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY i.scheduled_time DESC LIMIT #{offset}, #{pageSize}" +
            "</script>")
    @ResultMap("InterviewMap")
    List<Interview> findByStatusAndDateRangeAndPositionsAndScreenerPaginated(
        @Param("status") String status,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("positionIds") List<Long> positionIds,
        @Param("screener") String screener,
        @Param("offset") int offset, 
        @Param("pageSize") int pageSize,
        @Param("search") String search);

    @Select("<script>" +
            "SELECT COUNT(DISTINCT i.id) FROM interviews i " +
            "JOIN resume_positions rp ON i.resume_id = rp.resume_id " +
            "WHERE i.status = #{status} " +
            "AND i.scheduled_time BETWEEN #{startDate} AND #{endDate} " +
            "AND FIND_IN_SET(#{screener}, rp.screeners) > 0 " +
            "<if test='positionIds != null and positionIds.size() > 0'>" +
            "AND i.position_id IN " +
            "<foreach item='id' collection='positionIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "</if>" +
            "AND (#{search} IS NULL OR i.candidate LIKE CONCAT('%', #{search}, '%') OR i.interviewer LIKE CONCAT('%', #{search}, '%'))" +
            "</script>")
    long countByStatusAndDateRangeAndPositionsAndScreener(
        @Param("status") String status,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("positionIds") List<Long> positionIds,
        @Param("screener") String screener,
        @Param("search") String search);

    // 支持多状态筛选的方法
    @Select("<script>" +
            "SELECT * FROM interviews " +
            "WHERE scheduled_time BETWEEN #{startDate} AND #{endDate} " +
            "<if test='statuses != null and statuses.size() > 0'>" +
            "AND status IN " +
            "<foreach item='status' collection='statuses' open='(' separator=',' close=')'>" +
            "#{status}" +
            "</foreach> " +
            "</if>" +
            "<if test='positionIds != null and positionIds.size() > 0'>" +
            "AND position_id IN " +
            "<foreach item='id' collection='positionIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "</if>" +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY scheduled_time DESC LIMIT #{offset}, #{limit}" +
            "</script>")
    @ResultMap("InterviewMap")
    List<Interview> findAllPaginatedWithStatusesAndPositions(
        @Param("offset") int offset, 
        @Param("limit") int limit,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("search") String search,
        @Param("positionIds") List<Long> positionIds,
        @Param("statuses") List<String> statuses);

    @Select("<script>" +
            "SELECT COUNT(*) FROM interviews " +
            "WHERE scheduled_time BETWEEN #{startDate} AND #{endDate} " +
            "<if test='statuses != null and statuses.size() > 0'>" +
            "AND status IN " +
            "<foreach item='status' collection='statuses' open='(' separator=',' close=')'>" +
            "#{status}" +
            "</foreach> " +
            "</if>" +
            "<if test='positionIds != null and positionIds.size() > 0'>" +
            "AND position_id IN " +
            "<foreach item='id' collection='positionIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "</if>" +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%'))" +
            "</script>")
    long countAllWithStatusesAndPositions(
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("search") String search,
        @Param("positionIds") List<Long> positionIds,
        @Param("statuses") List<String> statuses);

    // 面试官权限的多状态筛选方法
    @Select("<script>" +
            "SELECT * FROM interviews " +
            "WHERE interviewer = #{interviewer} " +
            "AND scheduled_time BETWEEN #{startDate} AND #{endDate} " +
            "<if test='statuses != null and statuses.size() > 0'>" +
            "AND status IN " +
            "<foreach item='status' collection='statuses' open='(' separator=',' close=')'>" +
            "#{status}" +
            "</foreach> " +
            "</if>" +
            "<if test='positionIds != null and positionIds.size() > 0'>" +
            "AND position_id IN " +
            "<foreach item='id' collection='positionIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "</if>" +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY scheduled_time DESC LIMIT #{offset}, #{limit}" +
            "</script>")
    @ResultMap("InterviewMap")
    List<Interview> findByInterviewerPaginatedWithStatusesAndPositions(
        @Param("interviewer") String interviewer,
        @Param("offset") int offset, 
        @Param("limit") int limit,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("search") String search,
        @Param("positionIds") List<Long> positionIds,
        @Param("statuses") List<String> statuses);

    @Select("<script>" +
            "SELECT COUNT(*) FROM interviews " +
            "WHERE interviewer = #{interviewer} " +
            "AND scheduled_time BETWEEN #{startDate} AND #{endDate} " +
            "<if test='statuses != null and statuses.size() > 0'>" +
            "AND status IN " +
            "<foreach item='status' collection='statuses' open='(' separator=',' close=')'>" +
            "#{status}" +
            "</foreach> " +
            "</if>" +
            "<if test='positionIds != null and positionIds.size() > 0'>" +
            "AND position_id IN " +
            "<foreach item='id' collection='positionIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "</if>" +
            "AND (#{search} IS NULL OR candidate LIKE CONCAT('%', #{search}, '%') OR interviewer LIKE CONCAT('%', #{search}, '%'))" +
            "</script>")
    long countByInterviewerWithStatusesAndPositions(
        @Param("interviewer") String interviewer,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("search") String search,
        @Param("positionIds") List<Long> positionIds,
        @Param("statuses") List<String> statuses);

    // 筛选者权限的多状态筛选方法
    @Select("<script>" +
            "SELECT DISTINCT i.* FROM interviews i " +
            "JOIN resume_positions rp ON i.resume_id = rp.resume_id " +
            "WHERE FIND_IN_SET(#{screener}, rp.screeners) > 0 " +
            "AND i.scheduled_time BETWEEN #{startDate} AND #{endDate} " +
            "<if test='statuses != null and statuses.size() > 0'>" +
            "AND i.status IN " +
            "<foreach item='status' collection='statuses' open='(' separator=',' close=')'>" +
            "#{status}" +
            "</foreach> " +
            "</if>" +
            "<if test='positionIds != null and positionIds.size() > 0'>" +
            "AND i.position_id IN " +
            "<foreach item='id' collection='positionIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "</if>" +
            "AND (#{search} IS NULL OR i.candidate LIKE CONCAT('%', #{search}, '%') OR i.interviewer LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY i.scheduled_time DESC LIMIT #{offset}, #{limit}" +
            "</script>")
    @ResultMap("InterviewMap")
    List<Interview> findByScreenerPaginatedWithStatusesAndPositions(
        @Param("screener") String screener,
        @Param("offset") int offset, 
        @Param("limit") int limit,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("search") String search,
        @Param("positionIds") List<Long> positionIds,
        @Param("statuses") List<String> statuses);

    @Select("<script>" +
            "SELECT COUNT(DISTINCT i.id) FROM interviews i " +
            "JOIN resume_positions rp ON i.resume_id = rp.resume_id " +
            "WHERE FIND_IN_SET(#{screener}, rp.screeners) > 0 " +
            "AND i.scheduled_time BETWEEN #{startDate} AND #{endDate} " +
            "<if test='statuses != null and statuses.size() > 0'>" +
            "AND i.status IN " +
            "<foreach item='status' collection='statuses' open='(' separator=',' close=')'>" +
            "#{status}" +
            "</foreach> " +
            "</if>" +
            "<if test='positionIds != null and positionIds.size() > 0'>" +
            "AND i.position_id IN " +
            "<foreach item='id' collection='positionIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "</if>" +
            "AND (#{search} IS NULL OR i.candidate LIKE CONCAT('%', #{search}, '%') OR i.interviewer LIKE CONCAT('%', #{search}, '%'))" +
            "</script>")
    long countByScreenerWithStatusesAndPositions(
        @Param("screener") String screener,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate, 
        @Param("search") String search,
        @Param("positionIds") List<Long> positionIds,
        @Param("statuses") List<String> statuses);
}