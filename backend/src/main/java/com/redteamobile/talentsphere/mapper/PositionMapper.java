package com.redteamobile.talentsphere.mapper;

import com.redteamobile.talentsphere.model.Position;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface PositionMapper {
    @Insert({
        "INSERT INTO positions (title, level, department, workplace, requirements,",
        "description, is_active, headcount, filled, hire_source_type, interviewers, created_at, updated_at) ",
        "VALUES (#{title}, #{level}, #{department}, #{workplace}, #{requirements}, ",
        "#{description}, #{isActive}, #{headcount}, #{filled}, #{hireSourceType,typeHandler=com.redteamobile.talentsphere.config.HireSourceTypeEnumTypeHandler}, #{interviewers,typeHandler=com.redteamobile.talentsphere.typehandler.StringListTypeHandler}, NOW(), NOW())"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(Position position);

    @Update("UPDATE positions SET filled = filled + 1, updated_at = NOW() WHERE id = #{id}")
    void incrementFilled(Long id);

    @Select("SELECT p.* FROM positions p " +
            "JOIN resume_positions rp ON p.id = rp.position_id " +
            "WHERE rp.resume_id = #{resumeId} and p.is_active = true")
    @ResultMap("PositionMap")
    List<Position> selectResumePositions(Long resumeId);

    @Select("SELECT * FROM positions WHERE id = #{id}")
    @ResultMap("PositionMap")
    Position selectById(Long id);

    @Select("SELECT * FROM positions")
    @ResultMap("PositionMap")
    List<Position> selectAll();

    @Select("SELECT * FROM positions WHERE is_active = true")
    @ResultMap("PositionMap")
    List<Position> selectActive();  

    @Select("SELECT * FROM positions WHERE department = #{department}")
    @ResultMap("PositionMap")
    List<Position> selectByDepartment(String department);   

    @Update("UPDATE positions SET filled = filled - 1, updated_at = NOW() WHERE id = #{id} AND filled > 0")
    int decrementFilled(Long id);

    @Delete("DELETE FROM positions WHERE id = #{id}")
    void deleteById(Long id);

    @Update({
        "UPDATE positions SET title=#{title}, level=#{level}, department=#{department}, ",
        "workplace=#{workplace}, requirements=#{requirements}, description=#{description}, ",
        "is_active=#{isActive}, headcount=#{headcount}, filled=#{filled}, ",
        "hire_source_type=#{hireSourceType,typeHandler=com.redteamobile.talentsphere.config.HireSourceTypeEnumTypeHandler}, ",
        "interviewers=#{interviewers,typeHandler=com.redteamobile.talentsphere.typehandler.StringListTypeHandler}, ",
        "updated_at = NOW() ",
        "WHERE id=#{id}"
    })
    void update(Position position);
} 