package com.redteamobile.talentsphere.mapper;

import com.redteamobile.talentsphere.model.ResumeOperationLog;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface ResumeOperationLogMapper {

    @Insert("INSERT INTO resume_operation_logs (resume_id, operation, operated_by, remark) " +
            "VALUES (#{resumeId}, #{operation}, #{operatedBy}, #{remark})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(ResumeOperationLog log);

    @Select("SELECT * FROM resume_operation_logs WHERE id = #{id}")
    ResumeOperationLog findById(Long id);

    @Select("SELECT * FROM resume_operation_logs WHERE resume_id = #{resumeId} ORDER BY opt_time DESC")
    List<ResumeOperationLog> findByResumeId(Long resumeId);

    @Update("UPDATE resume_operation_logs SET " +
            "resume_id = #{resumeId}, " +
            "operation = #{operation}, " +
            "operated_by = #{operatedBy}, " +
            "remark = #{remark} " +
            "WHERE id = #{id}")
    int update(ResumeOperationLog log);

    @Delete("DELETE FROM resume_operation_logs WHERE id = #{id}")
    int delete(Long id);
} 