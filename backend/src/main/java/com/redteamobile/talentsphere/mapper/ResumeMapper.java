package com.redteamobile.talentsphere.mapper;

import java.time.LocalDate;
import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.redteamobile.talentsphere.dto.DailyCountRecord;
import com.redteamobile.talentsphere.dto.StatusCount;
import com.redteamobile.talentsphere.model.Resume;

@Mapper
public interface ResumeMapper {
    @Insert("INSERT INTO resumes (uuid, candidate_name, email, phone, source, file_type, file_path, status, import_date, imported_by) " +
            "VALUES (#{uuid}, #{candidateName}, #{email}, #{phone}, #{source}, #{fileType}, #{filePath}, #{status}, #{importDate}, #{importedBy})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(Resume resume);

    @Select("SELECT r.*, GROUP_CONCAT(p.id) as position_ids " +
            "FROM resumes r " +
            "LEFT JOIN resume_positions rp ON r.id = rp.resume_id " +
            "LEFT JOIN positions p ON rp.position_id = p.id " +
            "WHERE r.id = #{id} " +
            "GROUP BY r.id")
    Resume findById(@Param("id") Long id);

    @Update("UPDATE resumes SET candidate_name = #{candidateName}, email = #{email}, phone = #{phone}, talent_pool = #{talentPool}, last_modified = NOW() WHERE id = #{id}")
    int update(Resume resume);

    @Update("UPDATE resumes SET status = #{status}, last_modified = NOW() WHERE id = #{id}")
    void updateStatus(@Param("id") Long id,
                      @Param("status") String status);

    @Update("UPDATE resumes SET tags = #{tags}, last_modified = NOW() WHERE id = #{id}")
    void updateTags(@Param("id") Long id,
                    @Param("tags") String tags);

    @Select("SELECT * FROM resumes " +
            "WHERE (talent_pool IS NULL OR talent_pool = false) " +
            "AND (#{search} IS NULL OR candidate_name LIKE CONCAT('%', #{search}, '%') OR email LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY import_date DESC LIMIT #{offset}, #{pageSize}")
    List<Resume> findAll(@Param("offset") int offset,
                         @Param("pageSize") int pageSize,
                         @Param("search") String search);

    @Select("SELECT COUNT(*) FROM resumes " +
            "WHERE (talent_pool IS NULL OR talent_pool = false) " +
            "AND (#{search} IS NULL OR candidate_name LIKE CONCAT('%', #{search}, '%') OR email LIKE CONCAT('%', #{search}, '%'))")
    Long count(@Param("search") String search);

    @Select("SELECT * FROM resumes " +
            "WHERE status = #{status} " +
            "AND (talent_pool IS NULL OR talent_pool = false) " +
            "AND (#{search} IS NULL OR candidate_name LIKE CONCAT('%', #{search}, '%') OR email LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY import_date DESC LIMIT #{offset}, #{pageSize}")
    List<Resume> findByStatus(@Param("status") String status,
                              @Param("offset") int offset,
                              @Param("pageSize") int pageSize,
                              @Param("search") String search);

    @Select("SELECT COUNT(*) FROM resumes " +
            "WHERE status = #{status} " +
            "AND (talent_pool IS NULL OR talent_pool = false) " +
            "AND (#{search} IS NULL OR candidate_name LIKE CONCAT('%', #{search}, '%') OR email LIKE CONCAT('%', #{search}, '%'))")
    Long countByStatusValue(@Param("status") String status,
                           @Param("search") String search);

    @Delete("DELETE FROM resumes WHERE id = #{id}")
    void deleteById(@Param("id") Long id);

    @Delete("DELETE FROM resume_positions WHERE resume_id = #{resumeId}")
    void deleteResumePositions(@Param("resumeId") Long resumeId);

    @Select("SELECT r.* " +
            "FROM resumes r " +
            "LEFT JOIN resume_positions rp ON r.id = rp.resume_id " +
            "WHERE rp.screeners LIKE CONCAT('%', #{email}, '%') " +
            "AND (#{status} IS NULL OR r.status = #{status}) " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY r.import_date DESC LIMIT #{offset}, #{pageSize}")
    List<Resume> findByScreener(@Param("email") String email,
                                @Param("status") String status,
                                @Param("offset") int offset,
                                @Param("pageSize") int pageSize,
                                @Param("search") String search);

    @Select("SELECT COUNT(*) " +
            "FROM resumes r " +
            "LEFT JOIN resume_positions rp ON r.id = rp.resume_id " +
            "WHERE rp.screeners LIKE CONCAT('%', #{email}, '%') " +
            "AND (#{status} IS NULL OR r.status = #{status}) " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%'))")
    Long countByScreener(@Param("email") String email,
                         @Param("status") String status,
                         @Param("search") String search);

    @Select("SELECT status, COUNT(*) as count FROM resumes GROUP BY status")
    @Results(id = "statusCountMap", value = {
        @Result(property = "status", column = "status"),
        @Result(property = "count", column = "count")
    })
    List<StatusCount> countByStatus();

    @Select("SELECT DATE(import_date) as date, COUNT(*) as count " +
            "FROM resumes " +
            "WHERE DATE(import_date) BETWEEN #{startDate} AND #{endDate} " +
            "GROUP BY DATE(import_date)")
    List<DailyCountRecord> countDailyUploads(@Param("startDate") LocalDate startDate, 
                                             @Param("endDate") LocalDate endDate);

    @Select("SELECT r.* " +
            "FROM resumes r " +
            "JOIN resume_positions rp ON r.id = rp.resume_id " +
            "WHERE rp.position_id = #{positionId} " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY r.import_date DESC LIMIT #{offset}, #{pageSize}")
    List<Resume> findByPositionId(@Param("positionId") Long positionId,
                                 @Param("offset") int offset,
                                 @Param("pageSize") int pageSize,
                                 @Param("search") String search);
    
    @Select("SELECT COUNT(*) " +
            "FROM resumes r " +
            "JOIN resume_positions rp ON r.id = rp.resume_id " +
            "WHERE rp.position_id = #{positionId} " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%'))")
    Long countByPositionId(@Param("positionId") Long positionId,
                          @Param("search") String search);

    @Select("SELECT r.* " +
            "FROM resumes r " +
            "JOIN resume_positions rp ON r.id = rp.resume_id " +
            "WHERE rp.position_id = #{positionId} " +
            "AND rp.screeners LIKE CONCAT('%', #{email}, '%') " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%')) " +
            "ORDER BY r.import_date DESC LIMIT #{offset}, #{pageSize}")
    List<Resume> findByPositionIdAndScreener(@Param("positionId") Long positionId,
                                            @Param("email") String email,
                                            @Param("offset") int offset,
                                            @Param("pageSize") int pageSize,
                                            @Param("search") String search);

    @Select("SELECT COUNT(*) " +
            "FROM resumes r " +
            "JOIN resume_positions rp ON r.id = rp.resume_id " +
            "WHERE rp.position_id = #{positionId} " +
            "AND rp.screeners LIKE CONCAT('%', #{email}, '%') " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%'))")
    Long countByPositionIdAndScreener(@Param("positionId") Long positionId,
                                     @Param("email") String email,
                                     @Param("search") String search);

    // Combined filters for admin/HR users
    @Select("SELECT r.*, GROUP_CONCAT(p.id) as position_ids " +
            "FROM resumes r " +
            "LEFT JOIN resume_positions rp ON r.id = rp.resume_id " +
            "LEFT JOIN positions p ON rp.position_id = p.id " +
            "WHERE r.status = #{status} AND rp.position_id = #{positionId} " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%')) " +
            "GROUP BY r.id " +
            "ORDER BY r.import_date DESC LIMIT #{offset}, #{pageSize}")
    List<Resume> findByStatusAndPosition(@Param("status") String status,
                                        @Param("positionId") Long positionId,
                                        @Param("offset") int offset,
                                        @Param("pageSize") int pageSize,
                                        @Param("search") String search);

    @Select("SELECT COUNT(DISTINCT r.id) " +
            "FROM resumes r " +
            "JOIN resume_positions rp ON r.id = rp.resume_id " +
            "WHERE r.status = #{status} AND rp.position_id = #{positionId} " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%'))")
    Long countByStatusAndPosition(@Param("status") String status,
                                 @Param("positionId") Long positionId,
                                 @Param("search") String search);

    // Combined filters for screener users
    @Select("SELECT r.*, GROUP_CONCAT(p.id) as position_ids " +
            "FROM resumes r " +
            "LEFT JOIN resume_positions rp ON r.id = rp.resume_id " +
            "LEFT JOIN positions p ON rp.position_id = p.id " +
            "WHERE r.status = #{status} AND rp.position_id = #{positionId} " +
            "AND rp.screeners LIKE CONCAT('%', #{email}, '%') " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%')) " +
            "GROUP BY r.id " +
            "ORDER BY r.import_date DESC LIMIT #{offset}, #{pageSize}")
    List<Resume> findByScreenerStatusAndPosition(@Param("email") String email,
                                                @Param("status") String status,
                                                @Param("positionId") Long positionId,
                                                @Param("offset") int offset,
                                                @Param("pageSize") int pageSize,
                                                @Param("search") String search);

    @Select("SELECT COUNT(DISTINCT r.id) " +
            "FROM resumes r " +
            "JOIN resume_positions rp ON r.id = rp.resume_id " +
            "WHERE r.status = #{status} AND rp.position_id = #{positionId} " +
            "AND rp.screeners LIKE CONCAT('%', #{email}, '%') " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%'))")
    Long countByScreenerStatusAndPosition(@Param("email") String email,
                                         @Param("status") String status,
                                         @Param("positionId") Long positionId,
                                         @Param("search") String search);

    // New methods for list-based queries
    @Select("<script>" +
            "SELECT r.*, GROUP_CONCAT(p.id) as position_ids " +
            "FROM resumes r " +
            "LEFT JOIN resume_positions rp ON r.id = rp.resume_id " +
            "LEFT JOIN positions p ON rp.position_id = p.id " +
            "WHERE r.status IN " +
            "<foreach collection='statusList' item='status' open='(' separator=',' close=')'>" +
            "#{status}" +
            "</foreach> " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%')) " +
            "GROUP BY r.id " +
            "ORDER BY r.import_date DESC LIMIT #{offset}, #{pageSize}" +
            "</script>")
    List<Resume> findByStatusList(@Param("statusList") List<String> statusList,
                                  @Param("offset") int offset,
                                  @Param("pageSize") int pageSize,
                                  @Param("search") String search);

    @Select("<script>" +
            "SELECT COUNT(DISTINCT r.id) " +
            "FROM resumes r " +
            "WHERE r.status IN " +
            "<foreach collection='statusList' item='status' open='(' separator=',' close=')'>" +
            "#{status}" +
            "</foreach> " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%'))" +
            "</script>")
    Long countByStatusList(@Param("statusList") List<String> statusList,
                          @Param("search") String search);

    @Select("<script>" +
            "SELECT r.*, GROUP_CONCAT(p.id) as position_ids " +
            "FROM resumes r " +
            "LEFT JOIN resume_positions rp ON r.id = rp.resume_id " +
            "LEFT JOIN positions p ON rp.position_id = p.id " +
            "WHERE rp.position_id IN " +
            "<foreach collection='positionIdList' item='positionId' open='(' separator=',' close=')'>" +
            "#{positionId}" +
            "</foreach> " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%')) " +
            "GROUP BY r.id " +
            "ORDER BY r.import_date DESC LIMIT #{offset}, #{pageSize}" +
            "</script>")
    List<Resume> findByPositionIdList(@Param("positionIdList") List<Long> positionIdList,
                                     @Param("offset") int offset,
                                     @Param("pageSize") int pageSize,
                                     @Param("search") String search);

    @Select("<script>" +
            "SELECT COUNT(DISTINCT r.id) " +
            "FROM resumes r " +
            "JOIN resume_positions rp ON r.id = rp.resume_id " +
            "WHERE rp.position_id IN " +
            "<foreach collection='positionIdList' item='positionId' open='(' separator=',' close=')'>" +
            "#{positionId}" +
            "</foreach> " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%'))" +
            "</script>")
    Long countByPositionIdList(@Param("positionIdList") List<Long> positionIdList,
                              @Param("search") String search);

    @Select("<script>" +
            "SELECT r.*, GROUP_CONCAT(p.id) as position_ids " +
            "FROM resumes r " +
            "LEFT JOIN resume_positions rp ON r.id = rp.resume_id " +
            "LEFT JOIN positions p ON rp.position_id = p.id " +
            "WHERE r.status IN " +
            "<foreach collection='statusList' item='status' open='(' separator=',' close=')'>" +
            "#{status}" +
            "</foreach> " +
            "AND rp.position_id IN " +
            "<foreach collection='positionIdList' item='positionId' open='(' separator=',' close=')'>" +
            "#{positionId}" +
            "</foreach> " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%')) " +
            "GROUP BY r.id " +
            "ORDER BY r.import_date DESC LIMIT #{offset}, #{pageSize}" +
            "</script>")
    List<Resume> findByStatusListAndPositionList(@Param("statusList") List<String> statusList,
                                                @Param("positionIdList") List<Long> positionIdList,
                                                @Param("offset") int offset,
                                                @Param("pageSize") int pageSize,
                                                @Param("search") String search);

    @Select("<script>" +
            "SELECT COUNT(DISTINCT r.id) " +
            "FROM resumes r " +
            "JOIN resume_positions rp ON r.id = rp.resume_id " +
            "WHERE r.status IN " +
            "<foreach collection='statusList' item='status' open='(' separator=',' close=')'>" +
            "#{status}" +
            "</foreach> " +
            "AND rp.position_id IN " +
            "<foreach collection='positionIdList' item='positionId' open='(' separator=',' close=')'>" +
            "#{positionId}" +
            "</foreach> " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%'))" +
            "</script>")
    Long countByStatusListAndPositionList(@Param("statusList") List<String> statusList,
                                         @Param("positionIdList") List<Long> positionIdList,
                                         @Param("search") String search);

    // Screener-specific list queries
    @Select("<script>" +
            "SELECT r.*, GROUP_CONCAT(p.id) as position_ids " +
            "FROM resumes r " +
            "LEFT JOIN resume_positions rp ON r.id = rp.resume_id " +
            "LEFT JOIN positions p ON rp.position_id = p.id " +
            "WHERE r.status IN " +
            "<foreach collection='statusList' item='status' open='(' separator=',' close=')'>" +
            "#{status}" +
            "</foreach> " +
            "AND rp.screeners LIKE CONCAT('%', #{email}, '%') " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%')) " +
            "GROUP BY r.id " +
            "ORDER BY r.import_date DESC LIMIT #{offset}, #{pageSize}" +
            "</script>")
    List<Resume> findByScreenerAndStatusList(@Param("email") String email,
                                            @Param("statusList") List<String> statusList,
                                            @Param("offset") int offset,
                                            @Param("pageSize") int pageSize,
                                            @Param("search") String search);

    @Select("<script>" +
            "SELECT COUNT(DISTINCT r.id) " +
            "FROM resumes r " +
            "JOIN resume_positions rp ON r.id = rp.resume_id " +
            "WHERE r.status IN " +
            "<foreach collection='statusList' item='status' open='(' separator=',' close=')'>" +
            "#{status}" +
            "</foreach> " +
            "AND rp.screeners LIKE CONCAT('%', #{email}, '%') " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%'))" +
            "</script>")
    Long countByScreenerAndStatusList(@Param("email") String email,
                                     @Param("statusList") List<String> statusList,
                                     @Param("search") String search);

    @Select("<script>" +
            "SELECT r.*, GROUP_CONCAT(p.id) as position_ids " +
            "FROM resumes r " +
            "LEFT JOIN resume_positions rp ON r.id = rp.resume_id " +
            "LEFT JOIN positions p ON rp.position_id = p.id " +
            "WHERE rp.position_id IN " +
            "<foreach collection='positionIdList' item='positionId' open='(' separator=',' close=')'>" +
            "#{positionId}" +
            "</foreach> " +
            "AND rp.screeners LIKE CONCAT('%', #{email}, '%') " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%')) " +
            "GROUP BY r.id " +
            "ORDER BY r.import_date DESC LIMIT #{offset}, #{pageSize}" +
            "</script>")
    List<Resume> findByScreenerAndPositionIdList(@Param("email") String email,
                                                @Param("positionIdList") List<Long> positionIdList,
                                                @Param("offset") int offset,
                                                @Param("pageSize") int pageSize,
                                                @Param("search") String search);

    @Select("<script>" +
            "SELECT COUNT(DISTINCT r.id) " +
            "FROM resumes r " +
            "JOIN resume_positions rp ON r.id = rp.resume_id " +
            "WHERE rp.position_id IN " +
            "<foreach collection='positionIdList' item='positionId' open='(' separator=',' close=')'>" +
            "#{positionId}" +
            "</foreach> " +
            "AND rp.screeners LIKE CONCAT('%', #{email}, '%') " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%'))" +
            "</script>")
    Long countByScreenerAndPositionIdList(@Param("email") String email,
                                         @Param("positionIdList") List<Long> positionIdList,
                                         @Param("search") String search);

    @Select("<script>" +
            "SELECT r.*, GROUP_CONCAT(p.id) as position_ids " +
            "FROM resumes r " +
            "LEFT JOIN resume_positions rp ON r.id = rp.resume_id " +
            "LEFT JOIN positions p ON rp.position_id = p.id " +
            "WHERE r.status IN " +
            "<foreach collection='statusList' item='status' open='(' separator=',' close=')'>" +
            "#{status}" +
            "</foreach> " +
            "AND rp.position_id IN " +
            "<foreach collection='positionIdList' item='positionId' open='(' separator=',' close=')'>" +
            "#{positionId}" +
            "</foreach> " +
            "AND rp.screeners LIKE CONCAT('%', #{email}, '%') " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%')) " +
            "GROUP BY r.id " +
            "ORDER BY r.import_date DESC LIMIT #{offset}, #{pageSize}" +
            "</script>")
    List<Resume> findByScreenerStatusListAndPositionList(@Param("email") String email,
                                                        @Param("statusList") List<String> statusList,
                                                        @Param("positionIdList") List<Long> positionIdList,
                                                        @Param("offset") int offset,
                                                        @Param("pageSize") int pageSize,
                                                        @Param("search") String search);

    @Select("<script>" +
            "SELECT COUNT(DISTINCT r.id) " +
            "FROM resumes r " +
            "JOIN resume_positions rp ON r.id = rp.resume_id " +
            "WHERE r.status IN " +
            "<foreach collection='statusList' item='status' open='(' separator=',' close=')'>" +
            "#{status}" +
            "</foreach> " +
            "AND rp.position_id IN " +
            "<foreach collection='positionIdList' item='positionId' open='(' separator=',' close=')'>" +
            "#{positionId}" +
            "</foreach> " +
            "AND rp.screeners LIKE CONCAT('%', #{email}, '%') " +
            "AND (r.talent_pool IS NULL OR r.talent_pool = false) " +
            "AND (#{search} IS NULL OR r.candidate_name LIKE CONCAT('%', #{search}, '%') OR r.email LIKE CONCAT('%', #{search}, '%'))" +
            "</script>")
    Long countByScreenerStatusListAndPositionList(@Param("email") String email,
                                                 @Param("statusList") List<String> statusList,
                                                 @Param("positionIdList") List<Long> positionIdList,
                                                 @Param("search") String search);
    
    // 人才库相关方法
    @Select("<script>" +
            "SELECT * FROM resumes " +
            "WHERE talent_pool = true " +
            "AND is_duplicate = false " +
            "<if test='search != null and search != \"\"'>" +
            "AND (candidate_name LIKE CONCAT('%', #{search}, '%') " +
            "OR email LIKE CONCAT('%', #{search}, '%')) " +
            "</if>" +
            "ORDER BY last_modified DESC " +
            "LIMIT #{offset}, #{pageSize}" +
            "</script>")
    List<Resume> getTalentPoolResumes(@Param("offset") int offset, 
                                    @Param("pageSize") int pageSize, 
                                    @Param("search") String search);
    
    @Select("<script>" +
            "SELECT COUNT(*) FROM resumes " +
            "WHERE talent_pool = true " +
            "AND is_duplicate = false " +
            "<if test='search != null and search != \"\"'>" +
            "AND (candidate_name LIKE CONCAT('%', #{search}, '%') " +
            "OR email LIKE CONCAT('%', #{search}, '%')) " +
            "</if>" +
            "</script>")
    int getTalentPoolCount(@Param("search") String search);
}