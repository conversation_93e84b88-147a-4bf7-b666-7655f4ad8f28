package com.redteamobile.talentsphere.config;

import com.redteamobile.talentsphere.enums.HireSourceTypeEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * HireSourceTypeEnum枚举的MyBatis类型处理器
 */
public class HireSourceTypeEnumTypeHandler extends BaseTypeHandler<HireSourceTypeEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, HireSourceTypeEnum parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.name());
    }

    @Override
    public HireSourceTypeEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        return value == null ? null : HireSourceTypeEnum.valueOf(value);
    }

    @Override
    public HireSourceTypeEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        return value == null ? null : HireSourceTypeEnum.valueOf(value);
    }

    @Override
    public HireSourceTypeEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        return value == null ? null : HireSourceTypeEnum.valueOf(value);
    }
}