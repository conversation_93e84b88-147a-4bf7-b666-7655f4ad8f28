package com.redteamobile.talentsphere.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Document(collection = "resumes")
public class ResumeDocument {
    @Id
    private String id;
    @Indexed
    private String resumeId;  // Reference to MySQL resume UUID
    private String fileName;  // 文件名
    private String contentType;  // 文件类型    
    private String content;  // 小文件：Base64编码的字符串 (< 16MB)
    private String gridFsFileId;  // 大文件：GridFS文件ID (≥ 16MB)
    private long size;  // 文件大小
    private LocalDateTime uploadDate;  // 上传日期
    private String extractedText;  // 从简历中提取的文本内容
    private List<String> extractedSkills;  // 从简历中提取的技能
    private String uploadedBy;  // 上传人
    
    /**
     * 判断是否使用GridFS存储
     */
    public boolean isStoredInGridFS() {
        return gridFsFileId != null && !gridFsFileId.isEmpty();
    }
    
    /**
     * 判断是否使用Base64存储
     */
    public boolean isStoredInBase64() {
        return content != null && !content.isEmpty();
    }
} 