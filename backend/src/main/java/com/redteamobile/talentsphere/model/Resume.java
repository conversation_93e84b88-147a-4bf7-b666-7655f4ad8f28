package com.redteamobile.talentsphere.model;

import lombok.Data;
import java.time.LocalDateTime;

@Data
public class Resume {
    private Long id;  // 简历ID
    private String uuid;  // 简历UUID
    private String candidateName;  // 候选人姓名
    private String email;  // 候选人邮箱
    private String phone;  // 候选人电话
    private String source;  // 简历来源
    private String fileType;  // 简历文件类型
    private String filePath;
    private String status;
    private LocalDateTime importDate;  // 导入日期
    private String importedBy;  // 导入人
    private Boolean isDuplicate;  // 是否为重复简历
    private String tags;  // 简历标签
    private LocalDateTime lastModified;  // 最后修改日期
    private Boolean talentPool;  // 是否在人才库中
} 