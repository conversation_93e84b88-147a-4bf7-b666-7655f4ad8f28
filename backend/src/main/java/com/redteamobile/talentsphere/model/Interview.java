package com.redteamobile.talentsphere.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Interview {
    private Long id;  // 面试ID
    private Long positionId;  // 职位ID
    private Long resumeId;  // 简历ID
    private String candidate;  // 候选人姓名
    private String candidateEmail;  // 候选人邮箱
    private String interviewType;  // 面试类型
    private LocalDateTime scheduledTime;  // 面试时间
    private String interviewer;  // 面试官
    private String contact;  // 联系人
    private String ccList;  // 抄送邮箱
    private String status;  // 面试状态
    private Float rating;  // 面试评分
    private String result;  // 面试结果: 'PASS', 'REJECT', 'PENDING', null
    private String feedback;  // 面试反馈
    private String qrCodeUrl;  // 二维码URL
    private LocalDateTime checkinTime;  // 签到时间
    private String remark;  // 备注
    private String location;  // 面试方式
    private LocalDateTime updatedAt;  // 更新日期
} 