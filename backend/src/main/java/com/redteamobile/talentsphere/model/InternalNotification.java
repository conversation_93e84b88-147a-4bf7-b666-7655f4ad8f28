package com.redteamobile.talentsphere.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InternalNotification {
    private Long id;
    private String sender;
    private String receiver;
    private String status; // CREATED, READ
    private String content;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    private String referenceType; // RESUME, INTERVIEW
    private Long referenceId;
} 