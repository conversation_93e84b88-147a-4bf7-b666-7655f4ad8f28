package com.redteamobile.talentsphere.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResumePosition {
    private Long resumeId;
    private Long positionId;
    private LocalDateTime applicationDate;
    private String status;
    private List<String> screeners;
    
    // Additional fields for convenience
    private String positionTitle;
} 