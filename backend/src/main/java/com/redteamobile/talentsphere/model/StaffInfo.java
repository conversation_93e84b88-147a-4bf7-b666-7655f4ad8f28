package com.redteamobile.talentsphere.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StaffInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String staffId;
    private String staffName;
    private String email;
    private Boolean gender;
    private LocalDateTime startDate;
    private Boolean employStatus;
    private LocalDateTime createdDate;
    private LocalDateTime updatedDate;
} 