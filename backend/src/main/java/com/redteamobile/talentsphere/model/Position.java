package com.redteamobile.talentsphere.model;

import com.redteamobile.talentsphere.enums.HireSourceTypeEnum;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class Position {
    private Long id;  // 职位ID
    private String title;  // 职位名称
    private String level;  // 职位级别
    private String department;  // 部门
    private String workplace;  // 工作地点
    private String requirements;  // 职位要求
    private String description;  // 职位描述
    private Boolean isActive;  // 是否激活
    private Integer headcount;  // 招聘人数
    private Integer filled;  // 已招聘人数
    private HireSourceTypeEnum hireSourceType;  // 招聘来源类型：校招/社招
    private LocalDateTime createdAt;  // 创建日期
    private LocalDateTime updatedAt;  // 更新日期
    private List<String> interviewers; // 面试官
} 