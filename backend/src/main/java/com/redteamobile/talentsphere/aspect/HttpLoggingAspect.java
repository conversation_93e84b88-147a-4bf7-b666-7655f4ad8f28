package com.redteamobile.talentsphere.aspect;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Arrays;
import java.util.Objects;
import java.util.UUID;

@Aspect
@Component
@Slf4j
public class HttpLoggingAspect {

    @Around("execution(* com.redteamobile.talentsphere.controller..*.*(..))")
    public Object logHttpRequestResponse(ProceedingJoinPoint joinPoint) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String requestId = UUID.randomUUID().toString();
        long startTime = System.currentTimeMillis();

        // Log the incoming request
        log.info("[{}] Incoming Request: {} {} from {}",
                requestId,
                request.getMethod(),
                request.getRequestURI(),
                request.getRemoteAddr());

        log.debug("[{}] Request Parameters: {}", 
                requestId,
                Arrays.toString(joinPoint.getArgs()));

        try {
            // Execute the actual method
            Object result = joinPoint.proceed();
            
            // Log the response
            long duration = System.currentTimeMillis() - startTime;
            log.info("[{}] Outgoing Response: {} {} - Completed in {} ms",
                    requestId,
                    request.getMethod(),
                    request.getRequestURI(),
                    duration);

            log.debug("[{}] Response Body: {}", 
                    requestId,
                    result);

            return result;
        } catch (Exception e) {
            // Log the error response
            log.error("[{}] Error Response: {} {} - Failed in {} ms - Error: {}",
                    requestId,
                    request.getMethod(),
                    request.getRequestURI(),
                    System.currentTimeMillis() - startTime,
                    e.getMessage());
            throw e;
        }
    }
} 