package com.redteamobile.talentsphere.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SecurityAuditService {
    
    /**
     * 记录可疑活动
     */
    public void logSuspiciousActivity(String ipAddress, String userAgent, String activity, String details) {
        log.warn("SECURITY_ALERT: Suspicious activity detected - IP: {}, UserAgent: {}, Activity: {}, Details: {}", 
                ipAddress, userAgent, activity, details);
    }
    
    /**
     * 记录频率限制事件
     */
    public void logRateLimitExceeded(String ipAddress, String userAgent, String endpoint, String limitType) {
        log.warn("RATE_LIMIT_EXCEEDED: IP: {}, UserAgent: {}, Endpoint: {}, LimitType: {}", 
                ipAddress, userAgent, endpoint, limitType);
    }
    
    /**
     * 记录恶意请求
     */
    public void logMaliciousRequest(String ipAddress, String userAgent, String endpoint, String reason) {
        log.error("MALICIOUS_REQUEST: IP: {}, UserAgent: {}, Endpoint: {}, Reason: {}", 
                ipAddress, userAgent, endpoint, reason);
    }
    
    /**
     * 记录公共简历提交
     */
    public void logPublicResumeSubmission(String ipAddress, String userAgent, String candidateName, String email, Long positionId) {
        log.info("PUBLIC_RESUME_SUBMISSION: IP: {}, UserAgent: {}, Candidate: {}, Email: {}, Position: {}", 
                ipAddress, userAgent, candidateName, email, positionId);
    }
    
    /**
     * 记录文件上传安全检查
     */
    public void logFileUploadSecurity(String ipAddress, String filename, String contentType, long fileSize, boolean passed) {
        if (passed) {
            log.info("FILE_UPLOAD_SECURITY_CHECK_PASSED: IP: {}, File: {}, Type: {}, Size: {}", 
                    ipAddress, filename, contentType, fileSize);
        } else {
            log.warn("FILE_UPLOAD_SECURITY_CHECK_FAILED: IP: {}, File: {}, Type: {}, Size: {}", 
                    ipAddress, filename, contentType, fileSize);
        }
    }
}