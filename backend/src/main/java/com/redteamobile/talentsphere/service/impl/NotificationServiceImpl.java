package com.redteamobile.talentsphere.service.impl;

import com.redteamobile.talentsphere.dto.PageResponse;
import com.redteamobile.talentsphere.mapper.NotificationMapper;
import com.redteamobile.talentsphere.model.InternalNotification;
import com.redteamobile.talentsphere.service.NotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class NotificationServiceImpl implements NotificationService {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");

    @Autowired
    private NotificationMapper notificationMapper;

    @Override
    public InternalNotification createNotification(String sender, String receiver, String content, 
                                            String referenceType, Long referenceId) {
        InternalNotification notification = InternalNotification.builder()
                .sender(sender)
                .receiver(receiver)
                .status("CREATED")
                .content(content)
                .referenceType(referenceType)
                .referenceId(referenceId)
                .build();
        
        notificationMapper.insert(notification);
        return notification;
    }

    @Override
    public PageResponse<InternalNotification> getNotifications(String receiver, int page, int pageSize) {
        int offset = (page - 1) * pageSize;
        List<InternalNotification> notifications = notificationMapper.findByReceiver(receiver, offset, pageSize);
        int total = notificationMapper.countByReceiver(receiver);
        int totalPages = (total + pageSize - 1) / pageSize;
        
        return PageResponse.<InternalNotification>builder()
                .data(notifications)
                .total(total)
                .page(page)
                .pageSize(pageSize)
                .totalPages(totalPages)
                .build();
    }

    @Override
    public PageResponse<InternalNotification> getUnreadNotifications(String receiver, int page, int pageSize) {
        int offset = (page - 1) * pageSize;
        List<InternalNotification> notifications = notificationMapper.findByReceiverAndStatus(
                receiver, "CREATED", offset, pageSize);
        int total = notificationMapper.countByReceiverAndStatus(receiver, "CREATED");
        int totalPages = (total + pageSize - 1) / pageSize;
        
        return PageResponse.<InternalNotification>builder()
                .data(notifications)
                .total(total)
                .page(page)
                .pageSize(pageSize)
                .totalPages(totalPages)
                .build();
    }

    @Override
    public void markAsRead(Long notificationId) {
        notificationMapper.updateStatus(notificationId, "READ");
    }

    @Override
    public void markAllAsRead(String receiver) {
        notificationMapper.markAllAsRead(receiver);
    }

    @Override
    public int getUnreadCount(String receiver) {
        return notificationMapper.countByReceiverAndStatus(receiver, "CREATED");
    }

    @Override
    public void createResumeAssignmentNotification(String sender, String receiver, Long resumeId, String candidateName) {
        String content = String.format("%s 给你分配了一份简历需要筛选，候选人：%s", 
                sender, candidateName);
        createNotification(sender, receiver, content, "RESUME", resumeId);
    }

    @Override
    public void createInterviewScheduleNotification(String sender, String receiver, Long interviewId, 
                                                   String candidateName, String scheduledTime) {
        String formattedTime = DATE_TIME_FORMATTER.format(LocalDateTime.parse(scheduledTime));
        String content = String.format("%s 给你安排了 %s 与 %s 的面试", 
                sender, formattedTime, candidateName);
        createNotification(sender, receiver, content, "INTERVIEW", interviewId);
    }

    @Override
    public void createInterviewFeedbackNotification(String sender, String receiver, Long interviewId, 
                                                  String interviewer, String candidateName) {
        String content = String.format("%s 完成了对 %s 的面试反馈", 
                interviewer, candidateName);
        createNotification(sender, receiver, content, "INTERVIEW_FEEDBACK", interviewId);
    }

    @Override
    public void createInterviewUpdateNotification(String sender, String receiver, Long interviewId,
                                                String candidateName, String scheduledTime) {
        String formattedTime = DATE_TIME_FORMATTER.format(LocalDateTime.parse(scheduledTime));
        String content = String.format("%s 更新了 %s 与 %s 的面试安排", 
                sender, formattedTime, candidateName);
        createNotification(sender, receiver, content, "INTERVIEW", interviewId);
    }
} 