package com.redteamobile.talentsphere.service.impl;

import com.redteamobile.talentsphere.mapper.ResumeMapper;
import com.redteamobile.talentsphere.mapper.ResumePositionMapper;
import com.redteamobile.talentsphere.model.Resume;
import com.redteamobile.talentsphere.model.ResumePosition;
import com.redteamobile.talentsphere.model.User;
import com.redteamobile.talentsphere.service.NotificationService;
import com.redteamobile.talentsphere.service.ResumeOperationLogService;
import com.redteamobile.talentsphere.service.ResumePositionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class ResumePositionServiceImpl implements ResumePositionService {

    @Autowired
    private ResumeMapper resumeMapper;
    @Autowired
    private ResumePositionMapper resumePositionMapper;
    @Autowired
    private ResumeOperationLogService resumeOperationLogService;
    @Autowired
    private NotificationService notificationService;

    @Override
    public List<ResumePosition> getResumePositions(Long resumeId) {
        return resumePositionMapper.findByResumeId(resumeId);
    }

    @Override
    @Transactional
    public void updateResumePosition(Long id, Long positionId, String status, List<String> screeners) {
        // 先删除该简历的所有职位关联
        resumePositionMapper.deleteByResumeId(id);
        
        // 然后插入新的职位关联
        resumePositionMapper.insertOrUpdate(id, positionId, status, screeners);

        String operatedBy = getCurrentUser().getEmail();
        resumeOperationLogService.recordOperation(
                id,
                "UPDATE",
                operatedBy,
                String.format("更新职位关联（ID：%d）", positionId)
        );

        // Send notifications to screeners
        Resume resume = resumeMapper.findById(id);
        for (String screener : screeners) {
            if (!screener.equals(operatedBy)) {
                notificationService.createResumeAssignmentNotification(
                        operatedBy,
                        screener,
                        id,
                        resume.getCandidateName()
                );
            }
        }

    }   

    @Override
    @Transactional
    public void deleteResumePositions(Long resumeId) {
        resumePositionMapper.deleteByResumeId(resumeId);
    }

    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return (User) authentication.getPrincipal();
    }
} 