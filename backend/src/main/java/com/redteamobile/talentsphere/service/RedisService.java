package com.redteamobile.talentsphere.service;

import java.util.concurrent.TimeUnit;
import com.fasterxml.jackson.core.type.TypeReference;

/**
 * Redis操作服务接口
 */
public interface RedisService {

    /**
     * 存储数据
     * @param key 键
     * @param value 值
     */
    void set(String key, Object value);

    /**
     * 存储数据并设置过期时间
     * @param key 键
     * @param value 值
     * @param timeout 过期时间
     * @param unit 时间单位
     */
    void set(String key, Object value, long timeout, TimeUnit unit);

    /**
     * 获取数据
     * @param key 键
     * @param clazz 值类型
     * @return 值
     */
    <T> T get(String key, Class<T> clazz);

    /**
     * 获取复杂类型数据 (如 List<Position>)
     * @param key 键
     * @param typeReference 类型引用
     * @return 值
     */
    <T> T get(String key, TypeReference<T> typeReference);

    /**
     * 删除数据
     * @param key 键
     * @return 是否成功
     */
    boolean delete(String key);

    /**
     * 设置过期时间
     * @param key 键
     * @param timeout 过期时间
     * @param unit 时间单位
     * @return 是否成功
     */
    boolean expire(String key, long timeout, TimeUnit unit);

    /**
     * 判断是否存在
     * @param key 键
     * @return 是否存在
     */
    boolean hasKey(String key);

    /**
     * 获取过期时间
     * @param key 键
     * @return 过期时间（秒），-1表示永不过期，-2表示不存在
     */
    long getExpire(String key);
    
    /**
     * 向Set中添加元素
     * @param key 键
     * @param values 值
     * @return 添加成功的元素数量
     */
    Long sAdd(String key, String... values);
    
    /**
     * 获取Set中的所有元素
     * @param key 键
     * @return Set中的所有元素
     */
    java.util.Set<String> sMembers(String key);
    
    /**
     * 获取匹配模式的所有key
     * @param pattern 模式
     * @return 匹配的key集合
     */
    java.util.Set<String> keys(String pattern);
} 