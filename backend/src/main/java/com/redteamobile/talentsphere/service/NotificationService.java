package com.redteamobile.talentsphere.service;

import com.redteamobile.talentsphere.dto.PageResponse;
import com.redteamobile.talentsphere.model.InternalNotification;

public interface NotificationService {

    /**
     * 创建通知
     */
    InternalNotification createNotification(String sender, String receiver, String content, 
                                            String referenceType, Long referenceId);

    /**
     * 获取用户的通知
     */
    PageResponse<InternalNotification> getNotifications(String receiver, int page, int pageSize);

    /**
     * 获取用户的未读通知
     */
    PageResponse<InternalNotification> getUnreadNotifications(String receiver, int page, int pageSize);

    /**
     * 标记通知为已读
     */
    void markAsRead(Long notificationId);

    /**
     * 标记所有通知为已读
     */
    void markAllAsRead(String receiver);

    /**
     * 获取未读通知数量
     */
    int getUnreadCount(String receiver);
    
    /**
     * 创建简历分配通知
     */
    void createResumeAssignmentNotification(String sender, String receiver, Long resumeId, String candidateName);
    
    /**
     * 创建面试安排通知
     */
    void createInterviewScheduleNotification(String sender, String receiver, Long interviewId, String candidateName, String scheduledTime);
    
    /**
     * 创建面试反馈通知
     */
    void createInterviewFeedbackNotification(String sender, String receiver, Long interviewId, String interviewer, String candidateName);
    
    /**
     * 创建面试更新通知
     */
    void createInterviewUpdateNotification(String sender, String receiver, Long interviewId, String candidateName, String scheduledTime);
} 