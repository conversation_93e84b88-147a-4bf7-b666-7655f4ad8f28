package com.redteamobile.talentsphere.service.impl;

import com.google.common.collect.Lists;
import com.redteamobile.talentsphere.dto.PageResponse;
import com.redteamobile.talentsphere.dto.InterviewValidationResult;
import com.redteamobile.talentsphere.exception.InterviewSchedulingException;
import com.redteamobile.talentsphere.mapper.InterviewMapper;
import com.redteamobile.talentsphere.mapper.UserMapper;
import com.redteamobile.talentsphere.mapper.ResumePositionMapper;
import com.redteamobile.talentsphere.model.Interview;
import com.redteamobile.talentsphere.model.User;
import com.redteamobile.talentsphere.model.ResumePosition;
import com.redteamobile.talentsphere.service.DingTalkService;
import com.redteamobile.talentsphere.service.EmailService;
import com.redteamobile.talentsphere.service.InterviewService;
import com.redteamobile.talentsphere.service.NotificationService;
import com.redteamobile.talentsphere.service.PositionService;
import com.redteamobile.talentsphere.service.ResumeOperationLogService;
import com.redteamobile.talentsphere.service.ResumeService;
import com.redteamobile.talentsphere.service.RedisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.LinkedHashMap;

@Service
public class InterviewServiceImpl implements InterviewService {

    private static final Logger logger = LoggerFactory.getLogger(InterviewServiceImpl.class);

    public static final List<String> FULL_ACCESS_ROLES = Lists.newArrayList("ADMIN", "HR");

    @Value("${app.base-url}")
    private String appBaseUrl;

    @Autowired
    private InterviewMapper interviewMapper;
    @Autowired
    private EmailService emailService;
    @Autowired
    private ResumeOperationLogService resumeOperationLogService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private ResumeService resumeService;
    @Autowired
    private ResumePositionMapper resumePositionMapper;
    @Autowired
    private PositionService positionService;
    @Autowired
    private DingTalkService dingTalkService;
    @Autowired
    private RedisService redisService;
    
    private static final String CONTACT_HISTORY_KEY = "hr_shared_contact_history";

    @Override
    @Transactional
    public Interview scheduleInterview(
        Long positionId,
        Long resumeId,
        String candidate,
        String candidateEmail,
        String interviewer,
        String contact,
        List<String> cc,
        LocalDateTime scheduledTime,
        String interviewType,
        boolean notifyCandidate,
        String remark,
        String location
    ) {
        // 1. 验证是否可以安排新面试
        InterviewValidationResult validation = validateNewInterviewScheduling(resumeId);
        if (!validation.isCanSchedule()) {
            throw new InterviewSchedulingException(validation.getMessage(), validation.getBlockingInterview());
        }
        
        Interview interview = new Interview();
        interview.setResumeId(resumeId);
        interview.setPositionId(positionId);
        interview.setCandidate(candidate);
        interview.setCandidateEmail(candidateEmail);
        interview.setInterviewer(interviewer);
        interview.setContact(contact);
        interview.setCcList(String.join(",", cc));
        interview.setScheduledTime(scheduledTime);
        interview.setInterviewType(interviewType);
        interview.setStatus("SCHEDULED");
        interview.setRemark(remark);
        interview.setLocation(location);

        interviewMapper.insert(interview);

        // 保存联系方式到HR共享历史记录
        if (contact != null && !contact.trim().isEmpty()) {
            try {
                redisService.sAdd(CONTACT_HISTORY_KEY, contact.trim());
            } catch (Exception e) {
                logger.warn("Failed to save contact info to shared history", e);
            }
        }

        // Update resume status to SCHEDULED if it's currently SHORTLISTED
        try {
            resumeService.updateResumeStatus(resumeId, "SCHEDULED");
        } catch (Exception e) {
            logger.warn("Failed to update resume status to SCHEDULED for resumeId: {}", resumeId, e);
        }

        // Record operation log
        String operatedBy = getCurrentUser().getEmail();
        resumeOperationLogService.recordOperation(
            resumeId,
            "SCHEDULE",
            operatedBy,
            String.format("安排了%s面试，面试官为%s，面试时间为%s", 
                interviewType.equals("TECHNICAL") ? "技术" : 
                interviewType.equals("HR") ? "HR" : "其他",
                interviewer, 
                scheduledTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm")))
        );
        
        // Send notification to interviewer
        notificationService.createInterviewScheduleNotification(
            operatedBy, 
            interviewer, 
            interview.getId(), 
            candidate, 
            scheduledTime.toString()
        );

        if (notifyCandidate) {
            emailService.sendInterviewInvitation(candidateEmail, interview);
        }

        // Send DingTalk notification to interviewer
        try {
            String interviewTypeText = interviewType.equals("TECHNICAL") ? "技术面试" : 
                                     interviewType.equals("HR") ? "HR面试" : "其他面试";
            String formattedTime = scheduledTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm"));
            String positionName = positionService.getPosition(positionId).getTitle();
            
            dingTalkService.sendInterviewScheduledNotification(
                interviewer,
                candidate,
                positionName,
                formattedTime,
                interviewTypeText,
                location != null ? location : "待定",
                interview.getId()
            );
        } catch (Exception e) {
            logger.error("Failed to send DingTalk notification to interviewer", e);
        }

        return interview;
    }

    @Override
    public List<Interview> getInterviewsByResumeId(Long resumeId) {
        return interviewMapper.findByResumeId(resumeId);
    }

    @Override
    public void updateInterviewFeedback(Long interviewId, Float rating, String result, String feedback) {
        Interview interview = interviewMapper.findById(interviewId);
        if (interview == null) {
            throw new RuntimeException("Interview not found");
        }
        
        interview.setRating(rating);
        interview.setFeedback(feedback);
        interview.setResult(result);
        interview.setStatus("COMPLETED");
        interviewMapper.updateInterviewFeedback(interview);
        
        // Get HR users to notify
        User user = getCurrentUser();
        List<User> hrUsers = getHrUsers();
        
        // Send notifications to HR users
        for (User hrUser : hrUsers) {
            if (!hrUser.getEmail().equals(user.getEmail())) {
                notificationService.createInterviewFeedbackNotification(
                    user.getEmail(),
                    hrUser.getEmail(),
                    interviewId,
                    user.getEmail(),
                    interview.getCandidate()
                );
            }
        }
        
        // Send DingTalk notifications to HR users and resume screeners
        try {
            String formattedTime = interview.getScheduledTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm"));
            String resultText = "PASS".equals(result) ? "通过" : "REJECT".equals(result) ? "未通过" : "待定";
            String ratingText = rating != null ? String.format("%.1f/5.0", rating) : "无评分";
            String positionName = positionService.getPosition(interview.getPositionId()).getTitle();
            
            // 收集需要通知的用户（HR用户 + 简历筛选者，排除当前操作者）
            java.util.Set<String> notifyUsers = new java.util.HashSet<>();
            notifyUsers.addAll(getHrUserEmails());
            notifyUsers.addAll(getResumeScreeners(interview.getResumeId()));
            notifyUsers.remove(user.getEmail()); // 排除当前操作者
            
            if (!notifyUsers.isEmpty()) {
                Map<String, Object> params = new HashMap<>();
                params.put("candidateName", interview.getCandidate());
                params.put("positionName", positionName);
                params.put("interviewer", user.getEmail());
                params.put("interviewTime", formattedTime + " (CST)");
                params.put("result", resultText);
                params.put("rating", ratingText);
                params.put("interviewId", interviewId);
                
                dingTalkService.sendBatchNotification(
                    com.redteamobile.talentsphere.enums.DingTalkNotificationType.INTERVIEW_FEEDBACK,
                    new java.util.ArrayList<>(notifyUsers),
                    params
                );
            }
        } catch (Exception e) {
            logger.error("Failed to send DingTalk feedback notifications", e);
        }
    }

    @Override
    public void recordCheckin(Long interviewId) {
        interviewMapper.updateCheckinTime(interviewId, LocalDateTime.now());
    }

    @Override
    public String generateInterviewQRCode(Long interviewId) {
        // TODO: Implement QR code generation
        return "qr-code-url-" + interviewId;
    }

    @Override
    public PageResponse<Interview> getInterviews(int page, int pageSize, String search) {
        User user = getCurrentUser();
        List<Interview> interviews;
        long total;
        int offset = (page - 1) * pageSize;
        
        if (canFullAccess(user)) {
            interviews = interviewMapper.findAllPaginated(offset, pageSize, search);
            total = interviewMapper.countAll(search);
        } else {
            // 获取用户作为面试官的面试
            List<Interview> interviewerInterviews = interviewMapper.findByInterviewerPaginated(user.getEmail(), offset, pageSize, search);
            long interviewerTotal = interviewMapper.countByInterviewer(user.getEmail(), search);
            
            // 获取用户作为筛选者的面试
            List<Interview> screenerInterviews = interviewMapper.findByScreenerPaginated(user.getEmail(), offset, pageSize, search);
            long screenerTotal = interviewMapper.countByScreener(user.getEmail(), search);
            
            // 合并去重（简单处理：先用面试官的结果，如果不够再补充筛选者的）
            interviews = mergeInterviews(interviewerInterviews, screenerInterviews, pageSize);
            total = Math.max(interviewerTotal, screenerTotal); // 近似总数，实际可能有重复
        }
        
        return PageResponse.<Interview>builder()
            .data(interviews)
            .total(total)
            .page(page)
            .pageSize(pageSize)
            .build();
    }

    @Override
    public PageResponse<Interview> getInterviews(int page, int pageSize, LocalDate startDate, LocalDate endDate, String search, List<Long> positionIds) {
        User user = getCurrentUser();
        List<Interview> interviews;
        long total;
        int offset = (page - 1) * pageSize;
        
        // 如果没有提供日期范围和职位筛选，则调用原方法
        if (startDate == null && endDate == null && (positionIds == null || positionIds.isEmpty())) {
            return getInterviews(page, pageSize, search);
        }
        
        // 转换日期格式
        LocalDateTime startDateTime = startDate != null 
            ? startDate.atStartOfDay() 
            : LocalDateTime.now().minusYears(10); // 默认值为10年前
        
        LocalDateTime endDateTime = endDate != null 
            ? endDate.plusDays(1).atStartOfDay() 
            : LocalDateTime.now().plusYears(10); // 默认值为10年后
        
        if (canFullAccess(user)) {
            interviews = interviewMapper.findByDateRangeAndPositionsPaginated(startDateTime, endDateTime, positionIds, offset, pageSize, search);
            total = interviewMapper.countByDateRangeAndPositions(startDateTime, endDateTime, positionIds, search);
        } else {
            // 获取用户作为面试官的面试
            List<Interview> interviewerInterviews = interviewMapper.findByDateRangeAndPositionsAndInterviewerPaginated(
                startDateTime, endDateTime, positionIds, user.getEmail(), offset, pageSize, search);
            long interviewerTotal = interviewMapper.countByDateRangeAndPositionsAndInterviewer(startDateTime, endDateTime, positionIds, user.getEmail(), search);
            
            // 获取用户作为筛选者的面试
            List<Interview> screenerInterviews = interviewMapper.findByDateRangeAndPositionsAndScreenerPaginated(
                startDateTime, endDateTime, positionIds, user.getEmail(), offset, pageSize, search);
            long screenerTotal = interviewMapper.countByDateRangeAndPositionsAndScreener(startDateTime, endDateTime, positionIds, user.getEmail(), search);
            
            // 合并去重
            interviews = mergeInterviews(interviewerInterviews, screenerInterviews, pageSize);
            total = Math.max(interviewerTotal, screenerTotal); // 近似总数
        }
        
        return PageResponse.<Interview>builder()
            .data(interviews)
            .total(total)
            .page(page)
            .pageSize(pageSize)
            .build();
    }

    @Override
    public PageResponse<Interview> getInterviews(int page, int pageSize, LocalDate startDate, LocalDate endDate, String search, List<Long> positionIds, List<String> statuses) {
        User user = getCurrentUser();
        List<Interview> interviews;
        long total;
        int offset = (page - 1) * pageSize;
        
        // 如果没有状态筛选，调用原方法
        if (statuses == null || statuses.isEmpty()) {
            return getInterviews(page, pageSize, startDate, endDate, search, positionIds);
        }
        
        // 转换日期格式
        LocalDateTime startDateTime = startDate != null 
            ? startDate.atStartOfDay() 
            : LocalDateTime.now().minusYears(10); // 默认值为10年前
        
        LocalDateTime endDateTime = endDate != null 
            ? endDate.plusDays(1).atStartOfDay() 
            : LocalDateTime.now().plusYears(10); // 默认值为10年后
        
        if (canFullAccess(user)) {
            interviews = interviewMapper.findAllPaginatedWithStatusesAndPositions(
                offset, pageSize, startDateTime, endDateTime, search, positionIds, statuses);
            total = interviewMapper.countAllWithStatusesAndPositions(
                startDateTime, endDateTime, search, positionIds, statuses);
        } else {
            // 获取用户作为面试官的面试
            List<Interview> interviewerInterviews = interviewMapper.findByInterviewerPaginatedWithStatusesAndPositions(
                user.getEmail(), offset, pageSize, startDateTime, endDateTime, search, positionIds, statuses);
            long interviewerTotal = interviewMapper.countByInterviewerWithStatusesAndPositions(
                user.getEmail(), startDateTime, endDateTime, search, positionIds, statuses);
            
            // 获取用户作为筛选者的面试
            List<Interview> screenerInterviews = interviewMapper.findByScreenerPaginatedWithStatusesAndPositions(
                user.getEmail(), offset, pageSize, startDateTime, endDateTime, search, positionIds, statuses);
            long screenerTotal = interviewMapper.countByScreenerWithStatusesAndPositions(
                user.getEmail(), startDateTime, endDateTime, search, positionIds, statuses);
            
            // 合并去重
            interviews = mergeInterviews(interviewerInterviews, screenerInterviews, pageSize);
            total = Math.max(interviewerTotal, screenerTotal);
        }
        
        return PageResponse.<Interview>builder()
            .data(interviews)
            .total(total)
            .page(page)
            .pageSize(pageSize)
            .build();
    }

    @Override
    public PageResponse<Interview> getInterviews(int page, int pageSize, LocalDate startDate, LocalDate endDate, String search) {
        User user = getCurrentUser();
        List<Interview> interviews;
        long total;
        int offset = (page - 1) * pageSize;
        
        // 如果没有提供日期范围，则调用原方法
        if (startDate == null && endDate == null) {
            return getInterviews(page, pageSize, search);
        }
        
        // 转换日期格式
        LocalDateTime startDateTime = startDate != null 
            ? startDate.atStartOfDay() 
            : LocalDateTime.now().minusYears(10); // 默认值为10年前
        
        LocalDateTime endDateTime = endDate != null 
            ? endDate.plusDays(1).atStartOfDay() 
            : LocalDateTime.now().plusYears(10); // 默认值为10年后
        
        if (canFullAccess(user)) {
            interviews = interviewMapper.findByDateRangePaginated(startDateTime, endDateTime, offset, pageSize, search);
            total = interviewMapper.countByDateRange(startDateTime, endDateTime, search);
        } else {
            // 获取用户作为面试官的面试
            List<Interview> interviewerInterviews = interviewMapper.findByDateRangeAndInterviewerPaginated(
                startDateTime, endDateTime, user.getEmail(), offset, pageSize, search);
            long interviewerTotal = interviewMapper.countByDateRangeAndInterviewer(startDateTime, endDateTime, user.getEmail(), search);
            
            // 获取用户作为筛选者的面试
            List<Interview> screenerInterviews = interviewMapper.findByDateRangeAndScreenerPaginated(
                startDateTime, endDateTime, user.getEmail(), offset, pageSize, search);
            long screenerTotal = interviewMapper.countByDateRangeAndScreener(startDateTime, endDateTime, user.getEmail(), search);
            
            // 合并去重
            interviews = mergeInterviews(interviewerInterviews, screenerInterviews, pageSize);
            total = Math.max(interviewerTotal, screenerTotal); // 近似总数
        }
        
        return PageResponse.<Interview>builder()
            .data(interviews)
            .total(total)
            .page(page)
            .pageSize(pageSize)
            .build();
    }

    @Override
    public PageResponse<Interview> getInterviewsByStatus(String status, int page, int pageSize, String search) {
        User user = getCurrentUser();
        List<Interview> interviews;
        long total;
        int offset = (page - 1) * pageSize;

        if (canFullAccess(user)) {
            interviews = interviewMapper.findByStatusPaginated(status, offset, pageSize, search);
            total = interviewMapper.countByStatus(status, search);
        } else {
            // 获取用户作为面试官的面试
            List<Interview> interviewerInterviews = interviewMapper.findByStatusAndInterviewerPaginated(status, user.getEmail(), offset, pageSize, search);
            long interviewerTotal = interviewMapper.countByStatusAndInterviewer(status, user.getEmail(), search);

            // 获取用户作为筛选者的面试
            List<Interview> screenerInterviews = interviewMapper.findByStatusAndScreenerPaginated(status, user.getEmail(), offset, pageSize, search);
            long screenerTotal = interviewMapper.countByStatusAndScreener(status, user.getEmail(), search);

            // 合并去重
            interviews = mergeInterviews(interviewerInterviews, screenerInterviews, pageSize);
            total = Math.max(interviewerTotal, screenerTotal); // 近似总数
        }

        return PageResponse.<Interview>builder()
            .data(interviews)
            .total(total)
            .page(page)
            .pageSize(pageSize)
            .build();
    }



    @Override
    public PageResponse<Interview> getInterviewsByStatus(String status, int page, int pageSize, LocalDate startDate, LocalDate endDate, String search, List<Long> positionIds) {
        User user = getCurrentUser();
        List<Interview> interviews;
        long total;
        int offset = (page - 1) * pageSize;

        // 如果没有提供日期范围和职位筛选，则调用原方法
        if (startDate == null && endDate == null && (positionIds == null || positionIds.isEmpty())) {
            return getInterviewsByStatus(status, page, pageSize, search);
        }

        // 转换日期格式
        LocalDateTime startDateTime = startDate != null
            ? startDate.atStartOfDay()
            : LocalDateTime.now().minusYears(10); // 默认值为10年前

        LocalDateTime endDateTime = endDate != null
            ? endDate.plusDays(1).atStartOfDay()
            : LocalDateTime.now().plusYears(10); // 默认值为10年后

        if (canFullAccess(user)) {
            interviews = interviewMapper.findByStatusAndDateRangeAndPositionsPaginated(status, startDateTime, endDateTime, positionIds, offset, pageSize, search);
            total = interviewMapper.countByStatusAndDateRangeAndPositions(status, startDateTime, endDateTime, positionIds, search);
        } else {
            // 获取用户作为面试官的面试
            List<Interview> interviewerInterviews = interviewMapper.findByStatusAndDateRangeAndPositionsAndInterviewerPaginated(
                status, startDateTime, endDateTime, positionIds, user.getEmail(), offset, pageSize, search);
            long interviewerTotal = interviewMapper.countByStatusAndDateRangeAndPositionsAndInterviewer(
                status, startDateTime, endDateTime, positionIds, user.getEmail(), search);

            // 获取用户作为筛选者的面试
            List<Interview> screenerInterviews = interviewMapper.findByStatusAndDateRangeAndPositionsAndScreenerPaginated(
                status, startDateTime, endDateTime, positionIds, user.getEmail(), offset, pageSize, search);
            long screenerTotal = interviewMapper.countByStatusAndDateRangeAndPositionsAndScreener(
                status, startDateTime, endDateTime, positionIds, user.getEmail(), search);

            // 合并去重
            interviews = mergeInterviews(interviewerInterviews, screenerInterviews, pageSize);
            total = Math.max(interviewerTotal, screenerTotal); // 近似总数
        }

        return PageResponse.<Interview>builder()
            .data(interviews)
            .total(total)
            .page(page)
            .pageSize(pageSize)
            .build();
    }

    @Override
    public PageResponse<Interview> getInterviewsByStatus(String status, int page, int pageSize, LocalDate startDate, LocalDate endDate, String search) {
        User user = getCurrentUser();
        List<Interview> interviews;
        long total;
        int offset = (page - 1) * pageSize;

        // 如果没有提供日期范围，则调用原方法
        if (startDate == null && endDate == null) {
            return getInterviewsByStatus(status, page, pageSize, search);
        }

        // 转换日期格式
        LocalDateTime startDateTime = startDate != null
            ? startDate.atStartOfDay()
            : LocalDateTime.now().minusYears(10); // 默认值为10年前

        LocalDateTime endDateTime = endDate != null
            ? endDate.plusDays(1).atStartOfDay()
            : LocalDateTime.now().plusYears(10); // 默认值为10年后

        if (canFullAccess(user)) {
            interviews = interviewMapper.findByStatusAndDateRangePaginated(status, startDateTime, endDateTime, offset, pageSize, search);
            total = interviewMapper.countByStatusAndDateRange(status, startDateTime, endDateTime, search);
        } else {
            // 获取用户作为面试官的面试
            List<Interview> interviewerInterviews = interviewMapper.findByStatusAndDateRangeAndInterviewerPaginated(
                status, startDateTime, endDateTime, user.getEmail(), offset, pageSize, search);
            long interviewerTotal = interviewMapper.countByStatusAndDateRangeAndInterviewer(
                status, startDateTime, endDateTime, user.getEmail(), search);

            // 获取用户作为筛选者的面试
            List<Interview> screenerInterviews = interviewMapper.findByStatusAndDateRangeAndScreenerPaginated(
                status, startDateTime, endDateTime, user.getEmail(), offset, pageSize, search);
            long screenerTotal = interviewMapper.countByStatusAndDateRangeAndScreener(
                status, startDateTime, endDateTime, user.getEmail(), search);

            // 合并去重
            interviews = mergeInterviews(interviewerInterviews, screenerInterviews, pageSize);
            total = Math.max(interviewerTotal, screenerTotal); // 近似总数
        }

        return PageResponse.<Interview>builder()
            .data(interviews)
            .total(total)
            .page(page)
            .pageSize(pageSize)
            .build();
    }

    @Override
    public Interview cancelInterview(Long id) {
        Interview interview = interviewMapper.findById(id);
        if (interview == null) {
            throw new RuntimeException("Interview not found");
        }
        
        String originalInterviewer = interview.getInterviewer();
        interview.setStatus("CANCELLED");
        interview.setUpdatedAt(LocalDateTime.now());
        
        interviewMapper.update(interview);
        
        // Send DingTalk notification to original interviewer if not cancelled by themselves
        User currentUser = getCurrentUser();
        if (!currentUser.getEmail().equals(originalInterviewer)) {
            try {
                String interviewTypeText = interview.getInterviewType().equals("TECHNICAL") ? "技术面试" :
                                         interview.getInterviewType().equals("HR") ? "HR面试" : "其他面试";
                String formattedTime = interview.getScheduledTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm"));
                String positionName = positionService.getPosition(interview.getPositionId()).getTitle();
                
                dingTalkService.sendInterviewCancelledNotification(
                    originalInterviewer,
                    interview.getCandidate(),
                    positionName,
                    formattedTime + " (CST)",
                    interviewTypeText,
                    currentUser.getEmail(),
                    id
                );
            } catch (Exception e) {
                logger.error("Failed to send DingTalk cancellation notification to interviewer", e);
            }
        }
        
        return interview;
    }

    @Override
    @Transactional
    public Interview updateInterview(
        Long id,
        String interviewer,
        String contact,
        List<String> cc,
        LocalDateTime scheduledTime,
        String interviewType,
        boolean notifyCandidate,
        String remark,
        String location,
        Long positionId
    ) {
        Interview interview = interviewMapper.findById(id);
        if (interview == null) {
            throw new RuntimeException("Interview not found");
        }
        
        // Update interview fields
        interview.setInterviewer(interviewer);
        interview.setContact(contact);
        interview.setCcList(String.join(",", cc));
        interview.setScheduledTime(scheduledTime);
        interview.setInterviewType(interviewType);
        interview.setRemark(remark);
        interview.setLocation(location);
        interview.setPositionId(positionId);
        interview.setUpdatedAt(LocalDateTime.now());
        
        interviewMapper.update(interview);
        
        // 保存联系方式到HR共享历史记录
        if (contact != null && !contact.trim().isEmpty()) {
            try {
                redisService.sAdd(CONTACT_HISTORY_KEY, contact.trim());
            } catch (Exception e) {
                logger.warn("Failed to save contact info to shared history", e);
            }
        }
        
        // Record operation log
        String operatedBy = getCurrentUser().getEmail();
        String interviewTypeText = interviewType.equals("TECHNICAL") ? "技术面试" :
                                 interviewType.equals("HR") ? "HR面试" : "其他面试";
        String formattedTime = interview.getScheduledTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm"));
        resumeOperationLogService.recordOperation(
            interview.getResumeId(),
            "更新面试",
            operatedBy,
            String.format("更新了%s，面试官为%s，面试时间为%s",
                interviewTypeText,
                interviewer, 
                formattedTime)
        );
        
        // Send notification to interviewer if it has changed
        User user = getCurrentUser();
        if (!user.getEmail().equals(interviewer)) {
            notificationService.createInterviewUpdateNotification(
                operatedBy,
                interviewer,
                id,
                interview.getCandidate(),
                interview.getScheduledTime().toString()
            );
            
            // Send DingTalk notification to interviewer
            try {
                String positionName = positionService.getPosition(positionId).getTitle();
                dingTalkService.sendInterviewUpdatedNotification(
                    interviewer,
                    interview.getCandidate(),
                    positionName,                    
                    formattedTime + " (CST)",
                    interviewTypeText,
                    location != null ? location : "待定",
                    interview.getId()
                );
            } catch (Exception e) {
                logger.error("Failed to send DingTalk notification to interviewer", e);
            }
        }
        
        // Notify candidate if requested
        if (notifyCandidate && interview.getCandidateEmail() != null) {
            emailService.sendInterviewInvitation(interview.getCandidateEmail(), interview);
        }
        
        return interview;
    }

    @Override
    public Interview getInterviewById(Long id) {
        // 面试记录获取
        Interview interview = interviewMapper.findById(id);
        if (interview == null) {
            throw new RuntimeException("Interview not found with id: " + id);
        }
        
        // 权限检查
        User user = getCurrentUser();
        if (!canFullAccess(user) && 
            !user.getEmail().equals(interview.getInterviewer()) && 
            !isResumeScreener(interview.getResumeId(), user.getEmail())) {
            throw new RuntimeException("You don't have permission to view this interview");
        }
        
        return interview;
    }

    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return (User) authentication.getPrincipal();
    }

    private boolean canFullAccess(User user) {
        return FULL_ACCESS_ROLES.contains(user.getRole());
    }

    private List<User> getHrUsers() {
        return userMapper.findByRole("HR");
    }
    
    /**
     * 检查用户是否是某个简历的筛选者
     */
    private boolean isResumeScreener(Long resumeId, String userEmail) {
        List<ResumePosition> resumePositions = resumePositionMapper.findByResumeId(resumeId);
        for (ResumePosition resumePosition : resumePositions) {
            if (resumePosition.getScreeners() != null && resumePosition.getScreeners().contains(userEmail)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 合并面试官和筛选者的面试列表，使用Map去重并限制数量
     */
    private List<Interview> mergeInterviews(List<Interview> interviewerInterviews, List<Interview> screenerInterviews, int pageSize) {
        Map<Long, Interview> interviewMap = new LinkedHashMap<>();
        
        // 先添加面试官的面试
        for (Interview interview : interviewerInterviews) {
            if (interviewMap.size() >= pageSize) break;
            interviewMap.put(interview.getId(), interview);
        }
        
        // 再添加筛选者的面试（去重）
        for (Interview interview : screenerInterviews) {
            if (interviewMap.size() >= pageSize) break;
            interviewMap.putIfAbsent(interview.getId(), interview);
        }

        // 按照面试时间降序排序并限制数量
        return interviewMap.values().stream()
                .sorted((a, b) -> b.getScheduledTime().compareTo(a.getScheduledTime()))
                .limit(pageSize)
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public List<Interview> getInterviewFeedbackHistory(Long resumeId, String currentTime) {
        LocalDateTime currentDateTime = LocalDateTime.parse(currentTime);
        return interviewMapper.findCompletedInterviewsBeforeTime(resumeId, "COMPLETED", currentDateTime);
    }
    
    /**
     * 获取简历筛选者邮箱列表
     */
    private List<String> getResumeScreeners(Long resumeId) {
        return resumePositionMapper.findByResumeId(resumeId)
                .stream()
                .flatMap(rp -> rp.getScreeners().stream())
                .distinct()
                .collect(Collectors.toList());
    }
    
    /**
     * 获取HR用户邮箱列表
     */
    private List<String> getHrUserEmails() {
        return getHrUsers().stream()
                .map(User::getEmail)
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public InterviewValidationResult validateNewInterviewScheduling(Long resumeId) {
        // 1. 获取该候选人所有面试记录
        List<Interview> allInterviews = getInterviewsByResumeId(resumeId);
        
        // 2. 筛选出阻止新面试安排的面试（只有SCHEDULED状态）
        List<Interview> blockingInterviews = allInterviews.stream()
            .filter(interview -> "SCHEDULED".equals(interview.getStatus()))
            .collect(Collectors.toList());
        
        // 3. 判断是否可以安排新面试
        if (!blockingInterviews.isEmpty()) {
            // 获取最近的一个SCHEDULED面试（按时间排序）
            Interview latestBlockingInterview = blockingInterviews.stream()
                .sorted((a, b) -> b.getScheduledTime().compareTo(a.getScheduledTime()))
                .findFirst()
                .orElse(null);
                
            if (latestBlockingInterview != null) {
                InterviewValidationResult.InterviewInfo interviewInfo = InterviewValidationResult.InterviewInfo.builder()
                    .id(latestBlockingInterview.getId())
                    .interviewType(latestBlockingInterview.getInterviewType())
                    .scheduledTime(latestBlockingInterview.getScheduledTime())
                    .interviewer(latestBlockingInterview.getInterviewer())
                    .status(latestBlockingInterview.getStatus())
                    .interviewUrl(generateInterviewUrl(latestBlockingInterview.getId()))
                    .build();
                    
                String message = "当前候选人只能有一个\"已安排\"状态的面试";
                    
                return InterviewValidationResult.builder()
                    .canSchedule(false)
                    .message(message)
                    .blockingInterview(interviewInfo)
                    .build();
            }
        }
        
        return InterviewValidationResult.builder()
            .canSchedule(true)
            .message("可以安排新面试")
            .blockingInterview(null)
            .build();
    }
    
    /**
     * 生成面试详情页面URL
     */
    private String generateInterviewUrl(Long interviewId) {
        // 这里需要配置前端URL，暂时使用硬编码
        return String.format("%s/dashboard/interviews?interviewId=%d", appBaseUrl, interviewId);
    }
} 