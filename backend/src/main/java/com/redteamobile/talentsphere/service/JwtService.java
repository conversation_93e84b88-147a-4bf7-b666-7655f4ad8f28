package com.redteamobile.talentsphere.service;

import java.util.Map;
import org.springframework.security.core.userdetails.UserDetails;

public interface JwtService {
    String extractTempId(String token);
    String extractUserName(String token);
    String generateToken(UserDetails userDetails);
    boolean isTokenValid(String token, UserDetails userDetails);
    boolean isTokenValid(String token);
    String generateToken(Map<String, Object> claims, long expirationTimeMs);
}
