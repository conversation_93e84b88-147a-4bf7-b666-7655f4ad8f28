package com.redteamobile.talentsphere.service.impl;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.redteamobile.talentsphere.dto.HtmlToImageRequest;
import com.redteamobile.talentsphere.service.CanvasService;

import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;

@Slf4j
@Service
public class CanvasServiceImpl implements CanvasService {

    private final Environment environment;
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;

    @Autowired
    public CanvasServiceImpl(Environment environment, ObjectMapper objectMapper) {
        this.environment = environment;
        this.objectMapper = objectMapper;
        
        // 初始化OkHttpClient，设置超时参数
        this.httpClient = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .build();
    }

    @Override
    public byte[] renderImage(HtmlToImageRequest request) {
        if (request == null || request.getHtml() == null || request.getHtml().trim().isEmpty()) {
            throw new IllegalArgumentException("HTML content cannot be null or empty");
        }
        
        log.info("Converting HTML to image using Node.js HTML2Canvas service");
        
        // 获取Node.js服务的URL
        String html2canvasServiceUrl = environment.getProperty("html2canvas.service.url", "http://localhost:3102/render");
        
        try {
            // 构建请求体JSON
           String requestJson = objectMapper.writeValueAsString(request);
            
            // 创建请求
            RequestBody body = RequestBody.create(
                requestJson, 
                okhttp3.MediaType.parse("application/json; charset=utf-8")
            );
            
            Request httpRequest = new Request.Builder()
                .url(html2canvasServiceUrl)
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();
            
            // 发送请求
            log.info("Sending request to HTML2Canvas service: {}", html2canvasServiceUrl);
            Response response = httpClient.newCall(httpRequest).execute();
            
            // 检查响应状态
            if (response.isSuccessful()) {
                log.info("Successfully received PNG image from HTML2Canvas service");
                ResponseBody responseBody = response.body();
                if (responseBody != null) {
                    return responseBody.bytes();
                } else {
                    throw new RuntimeException("Empty response from HTML2Canvas service");
                }
            } else {
                log.error("HTML2Canvas service returned error: {}", response.code());
                String responseBody = response.body() != null ? response.body().string() : "No response body";
                throw new RuntimeException("Failed to convert HTML to image: " + response.code() + ", details: " + responseBody);
            }
        } catch (IOException e) {
            log.error("Network error calling HTML2Canvas service: {}", e.getMessage(), e);
            throw new RuntimeException("Error communicating with HTML2Canvas service", e);
        } catch (IllegalArgumentException e) {
            log.error("Invalid arguments for HTML2Canvas service: {}", e.getMessage(), e);
            throw new RuntimeException("Invalid parameters for HTML to image conversion", e);
        } catch (Exception e) {
            log.error("Unexpected error in HTML to image conversion: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to process HTML to image conversion", e);
        }
    }
} 