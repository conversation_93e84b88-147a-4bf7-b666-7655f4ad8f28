package com.redteamobile.talentsphere.service.impl;

import com.redteamobile.talentsphere.dto.UserSummary;
import com.redteamobile.talentsphere.mapper.UserMapper;
import com.redteamobile.talentsphere.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {
    
    private final UserMapper userMapper;
    
    @Override
    public List<UserSummary> searchUsersByEmail(String query, int limit) {
        return userMapper.searchByEmail(query, limit);
    }
    
    @Override
    public UserDetailsService userDetailsService() {
        return username -> {
            var user = userMapper.findByEmail(username);
            if (user == null) {
                throw new UsernameNotFoundException("User not found");
            }
            return user;
        };
    }
}