package com.redteamobile.talentsphere.service;

import com.redteamobile.talentsphere.dto.UserSummary;
import org.springframework.security.core.userdetails.UserDetailsService;
import java.util.List;

public interface UserService {
    
    /**
     * 根据邮箱模糊搜索用户
     * @param query 搜索关键字
     * @param limit 返回结果数量限制
     * @return 用户摘要列表
     */
    List<UserSummary> searchUsersByEmail(String query, int limit);
    
    /**
     * 返回UserDetailsService实例，用于Spring Security认证
     * @return UserDetailsService
     */
    UserDetailsService userDetailsService();
}