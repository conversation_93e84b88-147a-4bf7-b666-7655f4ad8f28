package com.redteamobile.talentsphere.service;

import com.redteamobile.talentsphere.model.ResumeScreening;

import java.util.List;

public interface ResumeScreeningService {
    ResumeScreening screenResume(Long resumeId, String jobRequirements);
    List<ResumeScreening> getScreeningsByResumeId(Long resumeId);
    void updateScreeningResult(Long screeningId, String result, String notes);
    Double calculateMatchingScore(ResumeScreening screening, String jobRequirements);
    /**
     * Quick preview screening of a resume
     * 
     * @param resumeId Resume to preview
     * @param isPass Whether the resume passes preview
     * @return The preview screening result
     */
    ResumeScreening previewResume(Long resumeId, boolean isPass);
} 