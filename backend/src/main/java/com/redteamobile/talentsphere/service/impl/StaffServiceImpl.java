package com.redteamobile.talentsphere.service.impl;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import com.redteamobile.talentsphere.dto.HtmlToImageRequest;
import com.redteamobile.talentsphere.dto.PageResponse;
import com.redteamobile.talentsphere.exception.ResourceNotFoundException;
import com.redteamobile.talentsphere.mapper.StaffInfoMapper;
import com.redteamobile.talentsphere.model.StaffInfo;
import com.redteamobile.talentsphere.service.CanvasService;
import com.redteamobile.talentsphere.service.EmailService;
import com.redteamobile.talentsphere.service.JwtService;
import com.redteamobile.talentsphere.service.RedisService;
import com.redteamobile.talentsphere.service.StaffService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class StaffServiceImpl implements StaffService {

    private static final String REDIS_TEMP_STAFF_PREFIX = "talentsphere:staff:temp:";
    private static final long STAFF_TOKEN_EXPIRATION = 24 * 60 * 60; // 24小时

    private final StaffInfoMapper staffInfoMapper;
    private final EmailService emailService;
    private final Environment environment;
    private final JwtService jwtService;
    private final TemplateEngine templateEngine;
    private final CanvasService canvasService;
    private final RedisService redisService;

    @Autowired
    public StaffServiceImpl(StaffInfoMapper staffInfoMapper, EmailService emailService, 
                            Environment environment, JwtService jwtService, 
                            TemplateEngine templateEngine, CanvasService canvasService,
                            RedisService redisService) {
        this.staffInfoMapper = staffInfoMapper;
        this.emailService = emailService;
        this.environment = environment;
        this.jwtService = jwtService;
        this.templateEngine = templateEngine;
        this.canvasService = canvasService;
        this.redisService = redisService;
    }

    @Override
    public List<StaffInfo> getAllStaff() {
        return staffInfoMapper.findAll();
    }

    @Override
    public PageResponse<StaffInfo> getAllStaffPaginated(int page, int size) {
        // Adjust page number to start from 0 for Spring Pageable
        int pageNumber = page - 1;
        if (pageNumber < 0) pageNumber = 0;
        
        // Get total count
        int total = staffInfoMapper.countAll();
        
        // Calculate pagination info
        int totalPages = (int) Math.ceil((double) total / size);
        
        // Get paginated data
        List<StaffInfo> staffList = staffInfoMapper.findAllPaginated(pageNumber * size, size);
        
        return PageResponse.<StaffInfo>builder()
                .data(staffList)
                .pageSize(size)
                .total(total)
                .totalPages(totalPages)
                .page(page)
                .build();
    }

    @Override
    public boolean importStaff(MultipartFile file) {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("File cannot be empty");
        }

        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            
            // Skip header row
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                String staffId = getCellValue(row.getCell(0));
                String name = getCellValue(row.getCell(1));
                String email = getCellValue(row.getCell(2));
                String genderStr = getCellValue(row.getCell(3));
                String startDateStr = getCellValue(row.getCell(4));
                
                // Validate required fields
                if (staffId.isEmpty() || name.isEmpty() || email.isEmpty() || genderStr.isEmpty() || startDateStr.isEmpty()) {
                    continue;
                }
                
                Boolean gender = "male".equalsIgnoreCase(genderStr) || "m".equalsIgnoreCase(genderStr);
                LocalDateTime startDate = java.time.LocalDate.parse(startDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                        .atStartOfDay(java.time.ZoneId.of("Asia/Shanghai"))
                        .toLocalDateTime();
                
                // Check if staff already exists
                StaffInfo existingStaff = staffInfoMapper.findByStaffId(staffId);
                if (existingStaff != null) {
                    // Update existing staff
                    existingStaff.setStaffName(name);
                    existingStaff.setEmail(email);
                    existingStaff.setGender(gender);
                    existingStaff.setStartDate(startDate);
                    staffInfoMapper.saveOrUpdate(existingStaff);
                } else {
                    // Create new staff
                    StaffInfo newStaff = StaffInfo.builder()
                            .staffId(staffId)
                            .staffName(name)
                            .email(email)
                            .gender(gender)
                            .startDate(startDate)
                            .employStatus(true)
                            .build();
                    staffInfoMapper.saveOrUpdate(newStaff);
                }
            }
            return true;
        } catch (IOException e) {
            log.error("Failed to import staff information", e);
            throw new RuntimeException("Failed to import staff information", e);
        }
    }

    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        return switch (cell.getCellType()) {
            case STRING -> cell.getStringCellValue();
            case NUMERIC -> {
                if (DateUtil.isCellDateFormatted(cell)) {
                    Date date = cell.getDateCellValue();
                    yield DateTimeFormatter.ofPattern("yyyy-MM-dd")
                            .format(new java.sql.Date(date.getTime()).toLocalDate());
                }
                yield String.valueOf((int) cell.getNumericCellValue());
            }
            case BOOLEAN -> String.valueOf(cell.getBooleanCellValue());
            case FORMULA -> cell.getCellFormula();
            default -> "";
        };
    }

    @Override
    public boolean sendBlessing(String staffId) {
        StaffInfo staff = staffInfoMapper.findByStaffId(staffId);
        if (staff == null) {
            throw new ResourceNotFoundException("Staff not found with ID: " + staffId);
        }
        
        return sendAnniversaryEmail(staff);
    }

    @Override
    public byte[] downloadAsImage(String token) {
        try {
            // Validate token and extract tempId
            if (!jwtService.isTokenValid(token)) {
                throw new RuntimeException("Invalid or expired token");
            }
            
            String tempId = jwtService.extractTempId(token);
            
            // Get StaffInfo from Redis
            StaffInfo staffInfo = redisService.get(REDIS_TEMP_STAFF_PREFIX + tempId, StaffInfo.class);
            if (staffInfo == null) {
                throw new RuntimeException("Staff information not found or expired");
            }
            
            // Calculate years of service
            LocalDateTime startDate = staffInfo.getStartDate();
            LocalDateTime now = LocalDateTime.now();
            int years = now.getYear() - startDate.getYear();
            
            // Prepare template content
            Context context = new Context();
            context.setVariable("staffNameCh", staffInfo.getStaffName());
            context.setVariable("years", years);
            // Format date as needed by the template
            context.setVariable("startDate", startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            
            // Generate HTML content - ensure path matches what's used in sendAnniversaryEmail
            String htmlContent = templateEngine.process("anniversary/" + years + "_img_v1.1", context);
            
            // Convert HTML to image using the new CanvasService
            return convertHtmlToImage(htmlContent);
        } catch (Exception e) {
            log.error("Failed to generate blessing image", e);
            throw new RuntimeException("Failed to generate blessing image", e);
        }
    }
    
    private byte[] convertHtmlToImage(String htmlContent) {
        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            throw new IllegalArgumentException("HTML content cannot be null or empty");
        }
        
        HtmlToImageRequest request = HtmlToImageRequest.builder()
            .html(htmlContent)
            .width(640)
            .height(1301)
            .fullPage(true)
            .waitTime(1000)
            .build();
        
        return canvasService.renderImage(request);
    }

    @Override
    @Scheduled(cron = "0 0 0 * * ?", zone = "Asia/Shanghai") // Run at midnight every day in China's time zone
    public void checkAndSendAnniversaryEmails() {
        List<StaffInfo> anniversaryStaff = staffInfoMapper.findStaffWithAnniversaryToday();
        log.info("Found {} staff with anniversaries today", anniversaryStaff.size());
        
        for (StaffInfo staff : anniversaryStaff) {
            try {
                sendAnniversaryEmail(staff);
            } catch (Exception e) {
                log.error("Failed to send anniversary email to staff {}: {}", staff.getStaffId(), e.getMessage());
            }
        }
    }
    
    private boolean sendAnniversaryEmail(StaffInfo staff) {
        try {
            // 生成临时token
            String tempToken = generateTempToken(staff);
            
            // 创建包含token的下载URL
            String baseUrl = environment.getProperty("app.base-url", "http://localhost:8095");
            String downloadUrl = baseUrl + "/api/staff/download/blessing-image?token=" + tempToken;

            // Calculate years of service
            LocalDateTime startDate = staff.getStartDate();
            LocalDateTime now = LocalDateTime.now();
            int years = now.getYear() - startDate.getYear();
            
            // Prepare email context
            String email = staff.getEmail();
            Context context = new Context();
            context.setVariable("staffNameCh", staff.getStaffName());
            context.setVariable("downloadUrl", downloadUrl);
            context.setVariable("hint", "* 如果您在使用深色模式，请将浏览器设置为浅色模式以获得最佳阅读体验。");
            
            // Send email
            emailService.sendAnniversaryEmail(
                "Happy Work Anniversary!",
                staff.getEmail(),
                context,
                years
            );
            
            log.info("Sent anniversary email to {}", email);
            return true;
        } catch (IllegalArgumentException | NullPointerException e) {
            log.error("Invalid data for anniversary email: {}", e.getMessage());
            return false;
        } catch (RuntimeException e) {
            log.error("Failed to send anniversary email: {}", e.getMessage());
            return false;
        }
    }

    private String generateTempToken(StaffInfo staffInfo) {
        // 生成唯一的tempId
        String tempId = UUID.randomUUID().toString();
        
        // 存储staffInfo到Redis
        String redisKey = REDIS_TEMP_STAFF_PREFIX + tempId;
        redisService.set(redisKey, staffInfo, STAFF_TOKEN_EXPIRATION, TimeUnit.SECONDS);
        
        // 创建临时token，包含tempId和24小时过期时间
        Map<String, Object> claims = new HashMap<>();
        claims.put("tempId", tempId);
        claims.put("type", "blessing-image");
        
        // 生成24小时有效期的token
        return jwtService.generateToken(claims, 24 * 60 * 60 * 1000);
    }
    
    @Override
    public boolean deleteStaff(String staffId) {
        try {
            StaffInfo staff = staffInfoMapper.findByStaffId(staffId);
            if (staff == null) {
                throw new ResourceNotFoundException("Staff not found with ID: " + staffId);
            }
            
            staffInfoMapper.deleteByStaffId(staffId);
            log.info("Successfully deleted staff with ID: {}", staffId);
            return true;
        } catch (ResourceNotFoundException e) {
            log.error("Failed to delete staff: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Error deleting staff: {}", e.getMessage());
            return false;
        }
    }
} 