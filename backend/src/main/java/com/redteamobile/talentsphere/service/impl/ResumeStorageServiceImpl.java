package com.redteamobile.talentsphere.service.impl;

import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.client.gridfs.GridFSBucket;
import com.mongodb.client.gridfs.GridFSDownloadStream;
import com.mongodb.client.gridfs.model.GridFSFile;
import com.redteamobile.talentsphere.model.ResumeDocument;
import com.redteamobile.talentsphere.repository.ResumeDocumentRepository;
import com.redteamobile.talentsphere.service.ResumeStorageService;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class ResumeStorageServiceImpl implements ResumeStorageService {

    private static final Logger logger = LoggerFactory.getLogger(ResumeStorageServiceImpl.class);
    
    // Base64编码会增大约33%的大小，所以阈值设为12MB以确保编码后不超过16MB
    private static final long GRIDFS_THRESHOLD = 12 * 1024 * 1024; // 12MB (Base64编码后约16MB)

    @Autowired
    private ResumeDocumentRepository resumeDocumentRepository;
    
    @Autowired
    private GridFsTemplate gridFsTemplate;
    
    @Autowired
    private GridFSBucket gridFSBucket;

    @Override
    public ResumeDocument storeResume(MultipartFile file, String resumeId, String uploadedBy) throws IOException {
        ResumeDocument document = new ResumeDocument();
        document.setResumeId(resumeId);
        document.setFileName(file.getOriginalFilename());
        document.setContentType(file.getContentType());
        document.setSize(file.getSize());
        document.setUploadDate(LocalDateTime.now());
        document.setUploadedBy(uploadedBy);

        // Extract text content first (needed for both storage methods)
        String textContent = extractTextContent(file);
        document.setExtractedText(textContent);

        // Extract skills
        List<String> skills = extractSkills(textContent);
        document.setExtractedSkills(skills);

        // 根据文件大小选择存储方式
        if (file.getSize() < GRIDFS_THRESHOLD) {
            // 小文件：使用Base64存储
            byte[] fileBytes = file.getBytes();
            String base64Content = Base64.getEncoder().encodeToString(fileBytes);
            long base64Size = base64Content.getBytes().length;
            
            logger.info("Storing file {} ({} bytes, Base64: {} bytes) using Base64 storage", 
                file.getOriginalFilename(), file.getSize(), base64Size);
            
            // 安全检查：确保Base64编码后不超过MongoDB限制
            if (base64Size > 15 * 1024 * 1024) { // 15MB安全阈值
                logger.warn("Base64 encoded size ({} bytes) is close to MongoDB limit, switching to GridFS", base64Size);
                // 切换到GridFS存储
                DBObject metaData = new BasicDBObject();
                metaData.put("resumeId", resumeId);
                metaData.put("uploadedBy", uploadedBy);
                
                ObjectId gridFsFileId = gridFsTemplate.store(
                    file.getInputStream(),
                    file.getOriginalFilename() != null ? file.getOriginalFilename() : "unknown",
                    file.getContentType(),
                    metaData
                );
                
                document.setGridFsFileId(gridFsFileId.toString());
            } else {
                document.setContent(base64Content);
            }
        } else {
            // 大文件：使用GridFS存储
            logger.info("Storing file {} ({} bytes) using GridFS storage", 
                file.getOriginalFilename(), file.getSize());
            DBObject metaData = new BasicDBObject();
            metaData.put("resumeId", resumeId);
            metaData.put("uploadedBy", uploadedBy);
            
            ObjectId gridFsFileId = gridFsTemplate.store(
                file.getInputStream(),
                file.getOriginalFilename() != null ? file.getOriginalFilename() : "unknown",
                file.getContentType(),
                metaData
            );
            
            document.setGridFsFileId(gridFsFileId.toString());
        }

        return resumeDocumentRepository.save(document);
    }

    @Override
    public ResumeDocument getResume(String id) {
        return resumeDocumentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Resume not found"));
    }

    @Override
    public ResumeDocument getByResumeId(String resumeId) {
        return resumeDocumentRepository.findByResumeId(resumeId)
                .orElseThrow(() -> new RuntimeException("Resume not found"));
    }

    /**
     * 获取简历内容的字节数组 - 混合存储兼容
     */
    @Override
    public byte[] getResumeContentBytes(ResumeDocument document) {
        if (document == null) {
            return new byte[0];
        }

        // 优先检查GridFS存储
        if (document.isStoredInGridFS()) {
            return getContentFromGridFS(document);
        }
        // 然后检查Base64存储
        else if (document.isStoredInBase64()) {
            return getContentFromBase64(document);
        }
        
        logger.warn("Document {} has no content stored", document.getId());
        return new byte[0];
    }
    
    /**
     * 从GridFS获取文件内容
     */
    private byte[] getContentFromGridFS(ResumeDocument document) {
        try {
            GridFSFile gridFSFile = gridFsTemplate.findOne(
                Query.query(Criteria.where("_id").is(document.getGridFsFileId()))
            );
            
            if (gridFSFile != null) {
                GridFSDownloadStream downloadStream = gridFSBucket.openDownloadStream(gridFSFile.getObjectId());
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int len;
                while ((len = downloadStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, len);
                }
                downloadStream.close();
                return outputStream.toByteArray();
            } else {
                logger.error("GridFS file not found: {}", document.getGridFsFileId());
            }
        } catch (Exception e) {
            logger.error("Failed to retrieve file from GridFS: {}", document.getGridFsFileId(), e);
        }
        return new byte[0];
    }
    
    /**
     * 从Base64获取文件内容
     */
    private byte[] getContentFromBase64(ResumeDocument document) {
        try {
            return Base64.getDecoder().decode(document.getContent());
        } catch (IllegalArgumentException e) {
            logger.error("Failed to decode Base64 content for document: {}", document.getId(), e);
            return new byte[0];
        }
    }

    @Override
    public void deleteResume(String id) {
        Optional<ResumeDocument> documentOpt = resumeDocumentRepository.findById(id);
        if (documentOpt.isPresent()) {
            ResumeDocument document = documentOpt.get();
            
            // 如果是GridFS存储，删除GridFS文件
            if (document.isStoredInGridFS()) {
                try {
                    gridFsTemplate.delete(Query.query(Criteria.where("_id").is(document.getGridFsFileId())));
                    logger.info("Deleted GridFS file: {}", document.getGridFsFileId());
                } catch (Exception e) {
                    logger.error("Failed to delete GridFS file: {}", document.getGridFsFileId(), e);
                }
            }
            
            // 删除文档记录
            resumeDocumentRepository.deleteById(id);
            logger.info("Deleted document: {}", id);
        }
    }

    @Override
    public String extractTextContent(MultipartFile file) throws IOException {
        String contentType = file.getContentType();
        if (contentType.equals("application/pdf")) {
            try (PDDocument document = PDDocument.load(file.getInputStream())) {
                PDFTextStripper stripper = new PDFTextStripper();
                return stripper.getText(document);
            }
        } else if (contentType.equals("application/vnd.openxmlformats-officedocument.wordprocessingml.document")) {
            try (XWPFDocument document = new XWPFDocument(file.getInputStream())) {
                XWPFWordExtractor extractor = new XWPFWordExtractor(document);
                return extractor.getText();
            }
        }
        return "";
    }

    @Override
    public List<String> extractSkills(String content) {
        List<String> skills = new ArrayList<>();
        // Add your skill extraction logic here
        // This is a simple example - you might want to use NLP or a predefined skill dictionary
        String[] commonSkills = {"java", "python", "javascript", "sql", "spring", "react"};
        
        for (String skill : commonSkills) {
            Pattern pattern = Pattern.compile("\\b" + skill + "\\b", Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(content);
            if (matcher.find()) {
                skills.add(skill);
            }
        }
        return skills;
    }
}