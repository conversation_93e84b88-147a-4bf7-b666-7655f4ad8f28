package com.redteamobile.talentsphere.service;

import com.redteamobile.talentsphere.model.Position;
import java.util.List;

public interface PositionService {
    Position createPosition(Position position);
    Position getPosition(Long id);
    List<Position> getPositions();
    List<Position> getActivePositions();
    List<Position> getPositionsByDepartment(String department);
    List<Position> getPositionsByResumeId(Long resumeId);
    Position updatePosition(Position position);
    void deletePosition(Long id);
}