package com.redteamobile.talentsphere.service.impl;

import com.redteamobile.talentsphere.mapper.PositionMapper;
import com.redteamobile.talentsphere.model.Position;
import com.redteamobile.talentsphere.service.PositionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class PositionServiceImpl implements PositionService {

    @Autowired
    private PositionMapper positionMapper;

    @Override
    @Transactional
    public Position createPosition(Position position) {
        // Set default values if not provided
        if (position.getIsActive() == null) {
            position.setIsActive(true);
        }
        if (position.getHeadcount() == null) {
            position.setHeadcount(1);
        }
        if (position.getFilled() == null) {
            position.setFilled(0);
        }

        positionMapper.insert(position);
        return position;
    }

    @Override
    public Position getPosition(Long id) {
        Position position = positionMapper.selectById(id);
        if (position == null) {
            throw new RuntimeException("Position not found with id: " + id);
        }
        return position;
    }

    @Override
    public List<Position> getPositions() {
        return positionMapper.selectAll();
    }

    @Override
    public List<Position> getActivePositions() {
        return positionMapper.selectActive();
    }

    @Override
    public List<Position> getPositionsByDepartment(String department) {
        return positionMapper.selectByDepartment(department);
    }

    @Override
    public List<Position> getPositionsByResumeId(Long resumeId) {
        return positionMapper.selectResumePositions(resumeId);
    }

    @Override
    @Transactional
    public Position updatePosition(Position position) {
        Position existing = getPosition(position.getId());
        
        // Validate headcount and filled
        if (position.getHeadcount() != null && position.getHeadcount() < existing.getFilled()) {
            throw new IllegalArgumentException("New headcount cannot be less than current filled positions");
        }

        positionMapper.update(position);
        return position;
    }

    @Override
    @Transactional
    public void deletePosition(Long id) {
        Position position = getPosition(id);
        if (position.getFilled() > 0) {
            throw new IllegalStateException("Cannot delete position with filled slots");
        }
        positionMapper.deleteById(id);
    }

} 