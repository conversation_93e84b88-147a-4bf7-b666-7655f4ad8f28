package com.redteamobile.talentsphere.service.impl;

import com.redteamobile.talentsphere.enums.DingTalkNotificationType;
import com.redteamobile.talentsphere.mapper.PositionMapper;
import com.redteamobile.talentsphere.mapper.UserMapper;
import com.redteamobile.talentsphere.model.Position;
import com.redteamobile.talentsphere.model.User;
import com.redteamobile.talentsphere.service.DingTalkService;
import com.redteamobile.talentsphere.service.RedisService;
import com.redteamobile.talentsphere.service.ResumeNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class ResumeNotificationServiceImpl implements ResumeNotificationService {
    
    private static final String SHORTLISTED_RESUMES_KEY = "shortlisted_resumes";
    private static final String POSITION_PREFIX = "position:";
    private static final int BATCH_THRESHOLD = 1; // 至少1份简历才发送通知
    
    @Autowired
    private RedisService redisService;
    
    @Autowired
    private DingTalkService dingTalkService;
    
    @Autowired
    private PositionMapper positionMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Override
    public void recordShortlistedResume(Long resumeId, Long positionId) {
        try {
            String key = SHORTLISTED_RESUMES_KEY + ":" + POSITION_PREFIX + positionId;
            // 将简历ID添加到Redis Set中，过期时间设为35分钟（比定时任务间隔稍长）
            redisService.sAdd(key, resumeId.toString());
            redisService.expire(key, 35, TimeUnit.MINUTES);
            
            log.info("Recorded shortlisted resume {} for position {}", resumeId, positionId);
        } catch (Exception e) {
            log.error("Failed to record shortlisted resume {} for position {}: {}", resumeId, positionId, e.getMessage());
        }
    }
    
    @Override
    @Scheduled(fixedRate = 30 * 60 * 1000) // 每30分钟执行一次
    public void checkAndSendBatchNotifications() {
        log.info("Starting batch notification check...");
        
        try {
            Map<Long, List<Long>> shortlistedByPosition = getShortlistedResumesByPosition();
            
            if (shortlistedByPosition.isEmpty()) {
                log.info("No shortlisted resumes found in the last 30 minutes");
                return;
            }
            
            long timestamp = System.currentTimeMillis();
            
            // 获取所有HR用户
            List<User> hrUsers = userMapper.findByRole("HR");
            if (hrUsers.isEmpty()) {
                log.warn("No HR users found to send notifications");
                return;
            }
            
            // 为每个有筛选简历的职位发送通知
            for (Map.Entry<Long, List<Long>> entry : shortlistedByPosition.entrySet()) {
                Long positionId = entry.getKey();
                List<Long> resumeIds = entry.getValue();
                
                if (resumeIds.size() >= BATCH_THRESHOLD) {
                    Position position = positionMapper.selectById(positionId);
                    if (position != null) {
                        sendBatchNotificationToHR(hrUsers, position, resumeIds.size(), timestamp);
                    }
                }
            }
            
            // 清空已处理的记录
            clearProcessedRecords();
            
            log.info("Batch notification check completed. Processed {} positions", shortlistedByPosition.size());
            
        } catch (Exception e) {
            log.error("Error during batch notification check: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public Map<Long, List<Long>> getShortlistedResumesByPosition() {
        Map<Long, List<Long>> result = new HashMap<>();
        
        try {
            // 获取所有shortlisted_resumes相关的key
            Set<String> keys = redisService.keys(SHORTLISTED_RESUMES_KEY + ":*");
            
            for (String key : keys) {
                // 从key中提取position ID
                String positionIdStr = key.substring((SHORTLISTED_RESUMES_KEY + ":" + POSITION_PREFIX).length());
                Long positionId = Long.parseLong(positionIdStr);
                
                // 获取该职位的所有简历ID
                Set<String> resumeIdStrs = redisService.sMembers(key);
                List<Long> resumeIds = new ArrayList<>();
                for (String resumeIdStr : resumeIdStrs) {
                    resumeIds.add(Long.parseLong(resumeIdStr));
                }
                
                if (!resumeIds.isEmpty()) {
                    result.put(positionId, resumeIds);
                }
            }
        } catch (Exception e) {
            log.error("Error getting shortlisted resumes by position: {}", e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public void clearProcessedRecords() {
        try {
            Set<String> keys = redisService.keys(SHORTLISTED_RESUMES_KEY + ":*");
            for (String key : keys) {
                redisService.delete(key);
            }
            log.info("Cleared {} processed shortlisted resume records", keys.size());
        } catch (Exception e) {
            log.error("Error clearing processed records: {}", e.getMessage());
        }
    }
    
    /**
     * 向HR发送批量筛选通知
     */
    private void sendBatchNotificationToHR(List<User> hrUsers, Position position, int resumeCount, long timestamp) {
        Map<String, Object> params = new HashMap<>();
        params.put("positionName", position.getTitle());
        params.put("resumeCount", resumeCount);
        params.put("positionId", position.getId());
        params.put("timestamp", timestamp);
        
        List<String> hrEmails = hrUsers.stream()
                .map(User::getEmail)
                .filter(Objects::nonNull)
                .toList();
        
        if (!hrEmails.isEmpty()) {
            dingTalkService.sendBatchNotification(DingTalkNotificationType.RESUME_SHORTLISTED_BATCH, hrEmails, params);
            log.info("Sent batch notification to {} HR users for position {} with {} resumes", 
                    hrEmails.size(), position.getTitle(), resumeCount);
        }
    }
}