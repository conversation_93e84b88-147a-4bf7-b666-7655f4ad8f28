package com.redteamobile.talentsphere.service.impl;

import com.redteamobile.talentsphere.dto.LoginResponse;
import com.redteamobile.talentsphere.dto.SignupResponse;
import com.redteamobile.talentsphere.dto.UserDTO;
import com.redteamobile.talentsphere.exception.ResourceNotFoundException;
import com.redteamobile.talentsphere.mapper.UserMapper;
import com.redteamobile.talentsphere.model.User;
import com.redteamobile.talentsphere.service.AuthService;
import com.redteamobile.talentsphere.service.DingTalkService;
import com.redteamobile.talentsphere.service.EmailService;
import com.redteamobile.talentsphere.service.JwtService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final UserMapper userMapper;
    private final AuthenticationManager authenticationManager;
    private final JwtService jwtService;
    private final EmailService emailService;
    private final DingTalkService dingTalkService;
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    private static final int RESET_CODE_EXPIRY = 10; // minutes

    // In-memory storage for reset codes with expiry time
    private static final Map<String, ResetCodeEntry> resetCodes = new ConcurrentHashMap<>();

    @Value("#{'${role.hr.users}'.split(',')}")
    private List<String> hrUsers;

    @Override
    public LoginResponse login(String email, String encodedPassword) {
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(email, encodedPassword)
        );

        User user = (User) authentication.getPrincipal();
        String token = jwtService.generateToken(user);

        return LoginResponse.builder()
                .token(token)
                .user(UserDTO.builder()
                        .email(user.getEmail())
                        .username(user.getUsername())
                        .role(user.getRole())
                        .build())
                .build();
    }

    @Override
    public SignupResponse signup(String email, String username, String password) {
        // Check if user already exists
        if (userMapper.findByEmail(email) != null) {
            throw new IllegalArgumentException("Email already registered");
        }

        // Query DingTalk userid by username
        String dingtalkUserId = dingTalkService.searchUserIdByName(username);
        if (dingtalkUserId == null) {
            throw new IllegalArgumentException("未找到对应的钉钉用户，请确认用户名与钉钉显示的中文名完全一致");
        }

        User user = User.builder()
                .email(email)
                .username(username)
                .password(passwordEncoder.encode(password))
                .role(hrUsers.contains(email) ? "HR" : "USER")
                .dingtalkUserid(dingtalkUserId)
                .build();

        userMapper.insert(user);
        String token = jwtService.generateToken(user);
        
        return SignupResponse.builder().token(token).build();
    }

    @Override
    public void sendResetCode(String email) {
        User user = userMapper.findByEmail(email);
        if (user == null) {
            throw new ResourceNotFoundException("Email not found");
        }

        String code = generateResetCode();
        resetCodes.put(email, new ResetCodeEntry(code, LocalDateTime.now()));
        emailService.sendPasswordResetEmail(email, code);
    }

    @Override
    public boolean verifyResetCode(String email, String code) {
        ResetCodeEntry entry = resetCodes.get(email);
        if (entry == null) {
            return false;
        }

        // Check if code has expired
        if (LocalDateTime.now().isAfter(entry.getExpiryTime())) {
            resetCodes.remove(email);
            return false;
        }

        return code != null && code.equals(entry.getCode());
    }

    @Override
    public void resetPassword(String email, String code, String newPassword) {
        if (!verifyResetCode(email, code)) {
            throw new IllegalArgumentException("Invalid or expired reset code");
        }

        User user = userMapper.findByEmail(email);
        if (user == null) {
            throw new ResourceNotFoundException("User not found");
        }

        user.setPassword(passwordEncoder.encode(newPassword));
        userMapper.updatePassword(user);
        
        // Delete the reset code after successful password reset
        resetCodes.remove(email);
    }

    private String generateResetCode() {
        Random random = new Random();
        return String.format("%06d", random.nextInt(1000000));
    }

    private static class ResetCodeEntry {
        private final String code;
        private final LocalDateTime expiryTime;

        public ResetCodeEntry(String code, LocalDateTime creationTime) {
            this.code = code;
            this.expiryTime = creationTime.plusMinutes(RESET_CODE_EXPIRY);
        }

        public String getCode() {
            return code;
        }

        public LocalDateTime getExpiryTime() {
            return expiryTime;
        }
    }
}
