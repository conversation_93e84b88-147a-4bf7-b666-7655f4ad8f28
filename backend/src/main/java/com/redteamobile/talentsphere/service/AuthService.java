package com.redteamobile.talentsphere.service;

import com.redteamobile.talentsphere.dto.LoginResponse;
import com.redteamobile.talentsphere.dto.SignupResponse;

public interface AuthService {
    LoginResponse login(String email, String encodedPassword);
    SignupResponse signup(String email, String username, String password);
    void sendResetCode(String email);
    boolean verifyResetCode(String email, String code);
    void resetPassword(String email, String code, String newPassword);
}
