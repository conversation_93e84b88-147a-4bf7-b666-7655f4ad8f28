package com.redteamobile.talentsphere.service.impl;

import com.redteamobile.talentsphere.mapper.ResumeScreeningMapper;
import com.redteamobile.talentsphere.model.ResumeScreening;
import com.redteamobile.talentsphere.service.ResumeScreeningService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Service
public class ResumeScreeningServiceImpl implements ResumeScreeningService {

    @Autowired
    private ResumeScreeningMapper screeningMapper;

    @Override
    public ResumeScreening screenResume(Long resumeId, String jobRequirements) {
        ResumeScreening screening = new ResumeScreening();
        screening.setResumeId(resumeId);
        screening.setScreeningDate(LocalDateTime.now());
        screening.setScreenedBy("SYSTEM");
        
        // Calculate matching score
        Double score = calculateMatchingScore(screening, jobRequirements);
        screening.setMatchingScore(score);
        
        // Set initial screening result based on score
        if (score >= 80.0) {
            screening.setScreeningResult("PASSED");
        } else if (score >= 60.0) {
            screening.setScreeningResult("PENDING_REVIEW");
        } else {
            screening.setScreeningResult("FAILED");
        }

        screeningMapper.insert(screening);
        return screening;
    }

    @Override
    public List<ResumeScreening> getScreeningsByResumeId(Long resumeId) {
        return screeningMapper.findByResumeId(resumeId);
    }

    @Override
    public void updateScreeningResult(Long screeningId, String result, String notes) {
        screeningMapper.updateResult(screeningId, result, notes);
    }

    @Override
    public Double calculateMatchingScore(ResumeScreening screening, String jobRequirements) {
        // Simple implementation of matching score calculation
        // This can be enhanced with more sophisticated algorithms
        Set<String> requiredSkills = new HashSet<>(Arrays.asList(jobRequirements.toLowerCase().split(",")));
        Set<String> candidateSkills = new HashSet<>(List.of(screening.getSkillTags().split(",")));
        
        int matchingSkills = 0;
        for (String skill : candidateSkills) {
            if (requiredSkills.contains(skill.toLowerCase())) {
                matchingSkills++;
            }
        }
        
        return (double) matchingSkills / requiredSkills.size() * 100;
    }

    @Override
    public ResumeScreening previewResume(Long resumeId, boolean isPass) {
        ResumeScreening screening = new ResumeScreening();
        screening.setResumeId(resumeId);
        screening.setScreeningDate(LocalDateTime.now());
        
        // Get current user as screener
        screening.setScreenedBy("currentUser");
        
        // Set preview-specific fields
        screening.setScreeningResult(isPass ? "PREVIEW_PASS" : "PREVIEW_FAIL");
        screening.setMatchingScore(isPass ? 100.0 : 0.0);
        screening.setScreeningNotes("Quick preview screening by interviewer");
        
        // Insert the preview result
        screeningMapper.insert(screening);
        
        return screening;
    }
} 