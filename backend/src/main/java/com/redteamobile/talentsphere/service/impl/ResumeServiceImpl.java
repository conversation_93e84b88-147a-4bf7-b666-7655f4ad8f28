package com.redteamobile.talentsphere.service.impl;

import com.google.common.collect.Lists;
import com.redteamobile.talentsphere.dto.PageResponse;
import com.redteamobile.talentsphere.mapper.InterviewMapper;
import com.redteamobile.talentsphere.mapper.PositionMapper;
import com.redteamobile.talentsphere.mapper.ResumeMapper;
import com.redteamobile.talentsphere.mapper.ResumePositionMapper;
import com.redteamobile.talentsphere.model.Interview;
import com.redteamobile.talentsphere.model.Resume;
import com.redteamobile.talentsphere.model.ResumeDocument;
import com.redteamobile.talentsphere.model.User;
import com.redteamobile.talentsphere.service.ResumeOperationLogService;
import com.redteamobile.talentsphere.service.ResumeService;
import com.redteamobile.talentsphere.service.ResumeStorageService;
import com.redteamobile.talentsphere.service.ResumeParserService;
import com.redteamobile.talentsphere.service.NotificationService;
import com.redteamobile.talentsphere.service.ResumeNotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.io.FilenameUtils;

@Service
public class ResumeServiceImpl implements ResumeService {

    private static final Logger logger = LoggerFactory.getLogger(ResumeServiceImpl.class);
    public static final List<String> FULL_ACCESS_ROLES = Lists.newArrayList("ADMIN", "HR");
    public static final String REGEX = "(?:\\】([\\u4e00-\\u9fa5]+)\\s\\d+年)|(?:-([\u4e00-\u9fa5]+)-)|(?:\\s([\\u4e00-\\u9fa5]+)\\s)";
    public static final Pattern PATTERN = Pattern.compile(REGEX);

    @Autowired
    private ResumeMapper resumeMapper;
    @Autowired
    private ResumeStorageService resumeStorageService;
    @Autowired
    private ResumePositionMapper resumePositionMapper;
    @Autowired
    private ResumeParserService resumeParserService;
    @Autowired
    private ResumeOperationLogService resumeOperationLogService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private ResumeNotificationService resumeNotificationService;
    @Autowired
    private InterviewMapper interviewMapper;

    @Override
    @Transactional
    public Resume importResume(MultipartFile file, String source, Long positionId, List<String> screeners) {
        try {
            // Store file in MongoDB
            String resumeId = UUID.randomUUID().toString();
            String importedBy = "Website".equals(source) ? source : getCurrentUser().getEmail();
            String name = extractName(file.getOriginalFilename(), PATTERN);
            ResumeDocument document = resumeStorageService.storeResume(file, resumeId, importedBy);

            // Parse resume
            Map<String, String> parsedData = resumeParserService.parseResume(document.getExtractedText());

        
            // Create MySQL resume entry
            Resume resume = new Resume();
            resume.setUuid(resumeId);
            resume.setCandidateName(name.equals("unknown") ? parsedData.get("name") : name);
            resume.setEmail(parsedData.get("email"));
            resume.setPhone(parsedData.get("phone_number"));
            resume.setSource(source);
            resume.setFileType(getFileExtension(file.getOriginalFilename()));
            resume.setStatus("NEW");
            resume.setImportDate(LocalDateTime.now());
            resume.setImportedBy(importedBy);

            resumeMapper.insert(resume);

            Long id = resume.getId();
            // Link resume to positions
            resumePositionMapper.insertOrUpdate(
                id,
                positionId,
                "APPLIED",
                screeners
            );
            
            // Log the import operation
            resumeOperationLogService.recordOperation(
                id,
                "IMPORT", 
                importedBy,
                String.format("从%s导入简历，应聘职位ID：%d", source, positionId)
            );

            for (String screener : screeners) {
                if (!screener.equals(importedBy)) {
                    notificationService.createResumeAssignmentNotification(
                            importedBy,
                            screener,
                            id,
                            resume.getCandidateName()
                    );
                }
            }

            return resume;
        } catch (IOException e) {
            throw new RuntimeException("Failed to import resume", e);
        }
    }

    @Override
    public Resume getResume(Long id) {
        return resumeMapper.findById(id);
    }

    @Override
    @Transactional
    public void updateResumeStatus(Long id, String status) {
        resumeMapper.updateStatus(id, status);
        
        String operatedBy = getCurrentUser().getEmail();
        String operation = switch (status.toUpperCase()) {
            case "REJECTED" -> "REJECT";
            case "SHORTLISTED" -> "SHORTLIST";
            case "SCHEDULED" -> "SCHEDULE";
            default -> "UPDATE";
        };

        resumeOperationLogService.recordOperation(
            id, 
            operation, 
            operatedBy,
            String.format("简历状态更新为：%s", 
                status.equals("SHORTLISTED") ? "入选" :
                status.equals("REJECTED") ? "已拒绝" : 
                status.equals("SCHEDULED") ? "已安排面试" : status)
        );
        
        // 如果状态更新为SHORTLISTED，记录到Redis用于批量通知
        if ("SHORTLISTED".equals(status.toUpperCase())) {
            try {
                // 获取简历关联的职位ID
                Resume resume = resumeMapper.findById(id);
                if (resume != null) {
                    // 通过resume_position表获取职位ID
                    Long positionId = resumePositionMapper.findPositionIdByResumeId(id);
                    if (positionId != null) {
                        resumeNotificationService.recordShortlistedResume(id, positionId);
                    }
                }
            } catch (Exception e) {
                // 记录失败不影响主流程
                logger.warn("Failed to record shortlisted resume {} for notification: {}", id, e.getMessage());
            }
        }
    }

    @Override
    public PageResponse<Resume> getAllResumes(int page, int pageSize, String search) {
        return getAllResumes(page, pageSize, search, null, null);
    }

    @Override
    public PageResponse<Resume> getAllResumes(int page, int pageSize, String search, List<String> statusList, List<Long> positionIdList) {
        User user = getCurrentUser();
        List<Resume> resumes;
        long total;
        int offset = (page - 1) * pageSize;

        boolean hasStatusFilter = statusList != null && !statusList.isEmpty();
        boolean hasPositionFilter = positionIdList != null && !positionIdList.isEmpty();

        if (canFullAccess(user)) {
            if (hasStatusFilter && hasPositionFilter) {
                // Both status and position filters (OR within each category)
                resumes = resumeMapper.findByStatusListAndPositionList(statusList, positionIdList, offset, pageSize, search);
                total = resumeMapper.countByStatusListAndPositionList(statusList, positionIdList, search);
            } else if (hasStatusFilter) {
                // Status filter only (OR among statuses)
                resumes = resumeMapper.findByStatusList(statusList, offset, pageSize, search);
                total = resumeMapper.countByStatusList(statusList, search);
            } else if (hasPositionFilter) {
                // Position filter only (OR among positions)
                resumes = resumeMapper.findByPositionIdList(positionIdList, offset, pageSize, search);
                total = resumeMapper.countByPositionIdList(positionIdList, search);
            } else {
                // No filters
                resumes = resumeMapper.findAll(offset, pageSize, search);
                total = resumeMapper.count(search);
            }
        } else {
            String userEmail = user.getEmail();
            if (hasStatusFilter && hasPositionFilter) {
                // Both status and position filters for screener (OR within each category)
                resumes = resumeMapper.findByScreenerStatusListAndPositionList(userEmail, statusList, positionIdList, offset, pageSize, search);
                total = resumeMapper.countByScreenerStatusListAndPositionList(userEmail, statusList, positionIdList, search);
            } else if (hasStatusFilter) {
                // Status filter only for screener (OR among statuses)
                resumes = resumeMapper.findByScreenerAndStatusList(userEmail, statusList, offset, pageSize, search);
                total = resumeMapper.countByScreenerAndStatusList(userEmail, statusList, search);
            } else if (hasPositionFilter) {
                // Position filter only for screener (OR among positions)
                resumes = resumeMapper.findByScreenerAndPositionIdList(userEmail, positionIdList, offset, pageSize, search);
                total = resumeMapper.countByScreenerAndPositionIdList(userEmail, positionIdList, search);
            } else {
                // No filters for screener
                resumes = resumeMapper.findByScreener(userEmail, null, offset, pageSize, search);
                total = resumeMapper.countByScreener(userEmail, null, search);
            }
        }

        return PageResponse.<Resume>builder()
                .data(resumes)
                .total(total)
                .page(page)
                .pageSize(pageSize)
                .totalPages((int) Math.ceil((double) total / pageSize))
                .build();
    }

    @Override
    public PageResponse<Resume> getResumesByStatus(String status, int page, int pageSize, String search) {
        User user = getCurrentUser();
        List<Resume> resumes;
        long total;
        int offset = (page - 1) * pageSize;

        if (canFullAccess(user)) {
            resumes = resumeMapper.findByStatus(status, offset, pageSize, search);
            total = resumeMapper.countByStatusValue(status, search);
        } else {
            resumes = resumeMapper.findByScreener(user.getEmail(), status, offset, pageSize, search);
            total = resumeMapper.countByScreener(user.getEmail(), status, search);
        }

        return PageResponse.<Resume>builder()
                .data(resumes)
                .total(total)
                .page(page)
                .pageSize(pageSize)
                .totalPages((int) Math.ceil((double) total / pageSize))
                .build();
    }

    @Override
    public PageResponse<Resume> getResumesByPosition(Long positionId, int page, int pageSize, String search) {
        int offset = (page - 1) * pageSize;
        User currentUser = getCurrentUser();
        List<Resume> resumes;
        long total;
        
        if (canFullAccess(currentUser)) {
            // Admin and HR can see all resumes
            resumes = resumeMapper.findByPositionId(positionId, offset, pageSize, search);
            total = resumeMapper.countByPositionId(positionId, search);
        } else {
            // Regular users can only see resumes they are screeners for
            resumes = resumeMapper.findByPositionIdAndScreener(positionId, currentUser.getEmail(), offset, pageSize, search);
            total = resumeMapper.countByPositionIdAndScreener(positionId, currentUser.getEmail(), search);
        }
        
        int totalPages = (int) Math.ceil((double) total / pageSize);
        
        return PageResponse.<Resume>builder()
                .data(resumes)
                .total(total)
                .page(page)
                .pageSize(pageSize)
                .totalPages(totalPages)
                .build();
    }

    @Override
    @Transactional
    public int updateResume(Resume resume, String source) {
        int result = resumeMapper.update(resume);
        
        if (result > 0) {
            String operatedBy = "Website".equals(source) ? "Website" : getCurrentUser().getEmail();
            resumeOperationLogService.recordOperation(
                resume.getId(), 
                "UPDATE", 
                operatedBy,
                "更新了简历信息"
            );
        }
        
        return result;
    }

    @Override
    @Transactional
    public void deleteResume(Long id) {
        // Get resume details before deletion
        Resume resume = resumeMapper.findById(id);
        if (resume == null) {
            throw new IllegalArgumentException("Resume not found with id: " + id);
        }

        try {
            String operatedBy = getCurrentUser().getEmail();
            
            // Log deletion before actually deleting
            resumeOperationLogService.recordOperation(
                id, 
                "DELETE", 
                operatedBy,
                String.format("删除了候选人 %s 的简历", resume.getCandidateName())
            );
            
            // Delete resume-position associations
            resumeMapper.deleteResumePositions(id);

            // Delete the resume record
            resumeMapper.deleteById(id);

            // Delete the resume file from MongoDB
            resumeStorageService.deleteResume(resume.getUuid());
        } catch (Exception e) {
            throw new RuntimeException("Failed to delete resume", e);
        }
    }

    /**
     * Extracts file extension from filename
     */
    private String getFileExtension(String filename) {
        if (filename == null) {
            return "unknown";
        }
        String extension = FilenameUtils.getExtension(filename).toLowerCase();
        // Validate supported extensions
        if (extension.equals("pdf") || 
            extension.equals("doc") || 
            extension.equals("docx")) {
            return extension;
        }
        return "unknown";
    }

    public static String extractName(String filename, Pattern pattern) {
        Matcher matcher = pattern.matcher(filename);
        
        if (matcher.find()) {
            // Check which capture group has a match
            for (int i = 1; i <= matcher.groupCount(); i++) {
                if (matcher.group(i) != null) {
                    return matcher.group(i);
                }
            }
        }
        
        // If the above regex doesn't match, try a more general pattern
        String chineseNameRegex = "([\\u4e00-\\u9fa5]{2,4})";
        Pattern namePattern = Pattern.compile(chineseNameRegex);
        Matcher nameMatcher = namePattern.matcher(filename);
        
        while (nameMatcher.find()) {
            String potentialName = nameMatcher.group(1);
            // Exclude potential job titles
            if (!potentialName.contains("工程师") && !potentialName.contains("招聘")) {
                return potentialName;
            }
        }
        
        return "unknown";
    }

    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !(authentication.getPrincipal() instanceof User)) {
            throw new RuntimeException("User not authenticated");
        }
        return (User) authentication.getPrincipal();
    }

    private boolean canFullAccess(User user) {
        return FULL_ACCESS_ROLES.contains(user.getRole());
    }
    
    @Override
    @Transactional
    public void moveToTalentPool(Long resumeId) {
        // 1. 检查简历是否存在
        Resume resume = resumeMapper.findById(resumeId);
        if (resume == null) {
            throw new IllegalArgumentException("简历不存在，ID: " + resumeId);
        }
        
        // 2. 检查是否已经在人才库中
        if (Boolean.TRUE.equals(resume.getTalentPool())) {
            throw new IllegalStateException("该简历已经在人才库中");
        }
        
        // 3. 检查是否有已安排的面试
        List<Interview> scheduledInterviews = interviewMapper.findByResumeId(resumeId)
            .stream()
            .filter(interview -> "SCHEDULED".equals(interview.getStatus()))
            .toList();
            
        if (!scheduledInterviews.isEmpty()) {
            throw new IllegalStateException("该简历有已安排的面试，无法移入人才库。请先完成或取消相关面试。");
        }
        
        // 4. 设置为人才库状态
        resume.setTalentPool(true);
        resume.setLastModified(LocalDateTime.now());
        resumeMapper.update(resume);
        
        // 5. 软删除职位关联（设置状态为TALENT_POOL）
        resumePositionMapper.moveToTalentPool(resumeId);
        
        // 6. 记录操作日志
        resumeOperationLogService.recordOperation(
            resumeId, 
            "MOVE_TO_TALENT_POOL", 
            getCurrentUser().getEmail(),
            "将简历移入人才库"
        );
    }
    
    @Override
    public PageResponse<Resume> getTalentPoolResumes(int page, int pageSize, String search) {
        int offset = (page - 1) * pageSize;
        
        List<Resume> resumes = resumeMapper.getTalentPoolResumes(offset, pageSize, search);
        int total = resumeMapper.getTalentPoolCount(search);
        
        return PageResponse.<Resume>builder()
                .data(resumes)
                .total(total)
                .page(page)
                .pageSize(pageSize)
                .totalPages((int) Math.ceil((double) total / pageSize))
                .build();
    }
    
    @Override
    @Transactional
    public void moveFromTalentPool(Long resumeId, Long positionId, List<String> screeners) {
        // 1. 检查简历是否存在且在人才库中
        Resume resume = resumeMapper.findById(resumeId);
        if (resume == null) {
            throw new IllegalArgumentException("简历不存在，ID: " + resumeId);
        }
        
        if (!Boolean.TRUE.equals(resume.getTalentPool())) {
            throw new IllegalStateException("该简历不在人才库中");
        }
        
        // 2. 设置为正常状态
        resume.setTalentPool(false);
        resume.setStatus("APPLIED");
        resume.setLastModified(LocalDateTime.now());
        resumeMapper.update(resume);
        
        // 3. 创建新的职位关联
        resumePositionMapper.insertOrUpdate(resumeId, positionId, "APPLIED", screeners);
        
        // 4. 记录操作日志
        resumeOperationLogService.recordOperation(
            resumeId, 
            "MOVE_FROM_TALENT_POOL", 
            getCurrentUser().getEmail(),
            "从人才库转入筛选流程，职位ID: " + positionId
        );
    }
} 