package com.redteamobile.talentsphere.service;

import com.redteamobile.talentsphere.model.ResumeDocument;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.util.List;

public interface ResumeStorageService {
    ResumeDocument storeResume(MultipartFile file, String resumeId, String uploadedBy) throws IOException;
    ResumeDocument getResume(String id);
    ResumeDocument getByResumeId(String resumeId);
    void deleteResume(String id);
    String extractTextContent(MultipartFile file) throws IOException;
    List<String> extractSkills(String content);
    
    /**
     * 获取简历内容的字节数组
     * @param document 简历文档
     * @return 文件内容的字节数组
     */
    byte[] getResumeContentBytes(ResumeDocument document);
} 