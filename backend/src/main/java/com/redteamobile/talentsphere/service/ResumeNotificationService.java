package com.redteamobile.talentsphere.service;

import java.util.List;
import java.util.Map;

/**
 * 简历通知服务接口
 */
public interface ResumeNotificationService {
    
    /**
     * 记录新筛选的简历
     * @param resumeId 简历ID
     * @param positionId 职位ID
     */
    void recordShortlistedResume(Long resumeId, Long positionId);
    
    /**
     * 检查并发送批量筛选通知
     */
    void checkAndSendBatchNotifications();
    
    /**
     * 获取指定时间段内按职位分组的筛选简历
     * @return Map<职位ID, 简历ID列表>
     */
    Map<Long, List<Long>> getShortlistedResumesByPosition();
    
    /**
     * 清空已处理的筛选简历记录
     */
    void clearProcessedRecords();
}