package com.redteamobile.talentsphere.service;

import com.redteamobile.talentsphere.dto.StatisticResponse;
import java.time.LocalDate;
import java.util.Map;

public interface StatisticService {
    /**
     * Get count of resumes by status
     */
    Map<String, Long> getResumeStatusCount();

    /**
     * Get resume upload trend between dates
     */
    Map<LocalDate, Long> getResumeUploadTrend(LocalDate startDate, LocalDate endDate);

    /**
     * Get count of interviews by status
     */
    Map<String, Long> getInterviewStatusCount();

    /**
     * Get interview trend between dates
     */
    Map<LocalDate, Long> getInterviewTrend(LocalDate startDate, LocalDate endDate);

    /**
     * Get overview statistics
     */
    StatisticResponse getOverview();
} 