package com.redteamobile.talentsphere.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import com.redteamobile.talentsphere.service.RedisService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class RedisServiceImpl implements RedisService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public void set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            log.debug("Redis set: key={}", key);
        } catch (Exception e) {
            log.error("Redis set error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    @Override
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        try {
            redisTemplate.opsForValue().set(key, value, timeout, unit);
            log.debug("Redis set with timeout: key={}, timeout={}, unit={}", key, timeout, unit);
        } catch (Exception e) {
            log.error("Redis set with timeout error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> clazz) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            if (value == null) {
                return null;
            }
            
            if (value.getClass().isAssignableFrom(clazz)) {
                return (T) value;
            } else {
                // 处理类型不匹配的情况，尝试进行转换
                return objectMapper.convertValue(value, clazz);
            }
        } catch (Exception e) {
            log.error("Redis get error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    @Override
    public <T> T get(String key, TypeReference<T> typeReference) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            if (value == null) {
                return null;
            }
            
            // 将缓存数据转换为指定的复杂类型
            String jsonString = objectMapper.writeValueAsString(value);
            return objectMapper.readValue(jsonString, typeReference);
        } catch (Exception e) {
            log.error("Redis get with TypeReference error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    @Override
    public boolean delete(String key) {
        try {
            Boolean result = redisTemplate.delete(key);
            log.debug("Redis delete: key={}, result={}", key, result);
            return result;
        } catch (Exception e) {
            log.error("Redis delete error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    @Override
    public boolean expire(String key, long timeout, TimeUnit unit) {
        try {
            Boolean result = redisTemplate.expire(key, timeout, unit);
            log.debug("Redis expire: key={}, timeout={}, unit={}, result={}", key, timeout, unit, result);
            return result;
        } catch (Exception e) {
            log.error("Redis expire error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    @Override
    public boolean hasKey(String key) {
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            log.error("Redis hasKey error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    @Override
    public long getExpire(String key) {
        try {
            Long expire = redisTemplate.getExpire(key);
            log.debug("Redis getExpire: key={}, expire={}", key, expire);
            return expire;
        } catch (Exception e) {
            log.error("Redis getExpire error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }
    
    @Override
    public Long sAdd(String key, String... values) {
        try {
            Long result = redisTemplate.opsForSet().add(key, (Object[]) values);
            log.debug("Redis sAdd: key={}, values={}, result={}", key, values, result);
            return result;
        } catch (Exception e) {
            log.error("Redis sAdd error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }
    
    @Override
    public java.util.Set<String> sMembers(String key) {
        try {
            java.util.Set<Object> members = redisTemplate.opsForSet().members(key);
            if (members == null) {
                return new java.util.HashSet<>();
            }
            return members.stream()
                    .map(Object::toString)
                    .collect(java.util.stream.Collectors.toSet());
        } catch (Exception e) {
            log.error("Redis sMembers error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }
    
    @Override
    public java.util.Set<String> keys(String pattern) {
        try {
            return redisTemplate.keys(pattern);
        } catch (Exception e) {
            log.error("Redis keys error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }
} 