package com.redteamobile.talentsphere.service;

import com.redteamobile.talentsphere.model.Interview;
import com.redteamobile.talentsphere.dto.PageResponse;
import com.redteamobile.talentsphere.dto.InterviewValidationResult;
import java.util.List;
import java.time.LocalDate;
import java.time.LocalDateTime;

public interface InterviewService {
    Interview scheduleInterview(
        Long positionId,
        Long resumeId,
        String candidate,
        String candidateEmail,
        String interviewer,
        String contact,
        List<String> cc,
        LocalDateTime scheduledTime,
        String interviewType,
        boolean notifyCandidate,
        String remark,
        String location
    );
    List<Interview> getInterviewsByResumeId(Long resumeId);
    void updateInterviewFeedback(Long interviewId, Float rating, String result, String feedback);
    void recordCheckin(Long interviewId);
    String generateInterviewQRCode(Long interviewId);
    PageResponse<Interview> getInterviews(int page, int pageSize, String search);
    PageResponse<Interview> getInterviews(int page, int pageSize, LocalDate startDate, LocalDate endDate, String search);
    PageResponse<Interview> getInterviews(int page, int pageSize, LocalDate startDate, LocalDate endDate, String search, List<Long> positionIds);
    PageResponse<Interview> getInterviews(int page, int pageSize, LocalDate startDate, LocalDate endDate, String search, List<Long> positionIds, List<String> statuses);
    PageResponse<Interview> getInterviewsByStatus(String status, int page, int pageSize, String search);
    PageResponse<Interview> getInterviewsByStatus(String status, int page, int pageSize, LocalDate startDate, LocalDate endDate, String search);
    PageResponse<Interview> getInterviewsByStatus(String status, int page, int pageSize, LocalDate startDate, LocalDate endDate, String search, List<Long> positionIds);
    Interview cancelInterview(Long id);
    List<Interview> getInterviewFeedbackHistory(Long resumeId, String currentTime);
    Interview updateInterview(Long id, String interviewer, String contact, List<String> cc, 
                            LocalDateTime scheduledTime, String interviewType, boolean notifyCandidate,
                            String remark, String location, Long positionId);
    Interview getInterviewById(Long id);
    InterviewValidationResult validateNewInterviewScheduling(Long resumeId);
} 