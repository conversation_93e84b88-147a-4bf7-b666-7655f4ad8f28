package com.redteamobile.talentsphere.service.impl;

import com.redteamobile.talentsphere.dto.DailyCountRecord;
import com.redteamobile.talentsphere.dto.StatisticResponse;
import com.redteamobile.talentsphere.dto.StatusCount;
import com.redteamobile.talentsphere.mapper.ResumeMapper;
import com.redteamobile.talentsphere.mapper.InterviewMapper;
import com.redteamobile.talentsphere.service.StatisticService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class StatisticServiceImpl implements StatisticService {

    @Autowired
    private ResumeMapper resumeMapper;

    @Autowired
    private InterviewMapper interviewMapper;

    @Override
    public Map<String, Long> getResumeStatusCount() {
        return resumeMapper.countByStatus()
                .stream()
                .collect(Collectors.toMap(
                    StatusCount::getStatus,
                    StatusCount::getCount
                ));
    }

    @Override
    public Map<LocalDate, Long> getResumeUploadTrend(LocalDate startDate, LocalDate endDate) {
        return resumeMapper.countDailyUploads(startDate, endDate)
                .stream()
                .collect(Collectors.toMap(
                    DailyCountRecord::getDate,
                    DailyCountRecord::getCount
                ));
    }

    @Override
    public Map<String, Long> getInterviewStatusCount() {
        return interviewMapper.countByStatusForStatistic()
                .stream()
                .collect(Collectors.toMap(
                    StatusCount::getStatus,
                    StatusCount::getCount
                ));
    }

    @Override
    public Map<LocalDate, Long> getInterviewTrend(LocalDate startDate, LocalDate endDate) {
        return interviewMapper.countDailyInterviews(startDate, endDate)
                .stream()
                .collect(Collectors.toMap(
                    DailyCountRecord::getDate,
                    DailyCountRecord::getCount
                ));
    }

    @Override
    public StatisticResponse getOverview() {
        Map<String, Long> resumeStats = getResumeStatusCount();
        Map<String, Long> interviewStats = getInterviewStatusCount();

        long totalResumes = resumeStats.values().stream().mapToLong(Long::longValue).sum();
        long shortlistedResumes = resumeStats.getOrDefault("SHORTLISTED", 0L);
        long rejectedResumes = resumeStats.getOrDefault("REJECTED", 0L);
        long totalInterviews = interviewStats.values().stream().mapToLong(Long::longValue).sum();
        long pendingInterviews = interviewStats.getOrDefault("PENDING", 0L);
        long passedInterviews = interviewStats.getOrDefault("PASSED", 0L);

        double shortlistRate = totalResumes > 0 ? (double) shortlistedResumes / totalResumes * 100 : 0;
        double passRate = totalInterviews > 0 ? (double) passedInterviews / totalInterviews * 100 : 0;

        return StatisticResponse.builder()
                .totalResumes(totalResumes)
                .totalInterviews(totalInterviews)
                .pendingInterviews(pendingInterviews)
                .shortlistedResumes(shortlistedResumes)
                .rejectedResumes(rejectedResumes)
                .shortlistRate(shortlistRate)
                .passRate(passRate)
                .build();
    }
} 