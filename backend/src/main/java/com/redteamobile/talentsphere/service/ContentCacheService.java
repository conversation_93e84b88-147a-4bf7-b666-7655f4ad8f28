package com.redteamobile.talentsphere.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.redteamobile.talentsphere.model.Position;
import com.redteamobile.talentsphere.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class ContentCacheService {
    
    private static final String ACTIVE_POSITIONS_CACHE_KEY = "positions:active";
    private static final long CACHE_EXPIRATION_HOURS = 24;
    
    @Autowired
    private RedisService redisService;
    
    @Autowired
    private PositionService positionService;
    
    /**
     * 获取活跃职位列表（优先从缓存获取）
     * @return 活跃职位列表
     */
    public List<Position> getActivePositions() {
        try {
            // 尝试从缓存获取
            List<Position> cachedPositions = redisService.get(
                ACTIVE_POSITIONS_CACHE_KEY, 
                new TypeReference<List<Position>>() {}
            );
            
            if (cachedPositions != null) {
                log.debug("Retrieved active positions from cache");
                return cachedPositions;
            }
            
            // 缓存未命中，从数据库获取
            log.debug("Cache miss for active positions, fetching from database");
            List<Position> activePositions = positionService.getActivePositions();
            
            // 存入缓存
            cacheActivePositions(activePositions);
            
            return activePositions;
            
        } catch (Exception e) {
            log.error("Error retrieving active positions from cache, falling back to database", e);
            // 缓存出错时直接从数据库获取
            return positionService.getActivePositions();
        }
    }
    
    /**
     * 缓存活跃职位列表
     * @param positions 职位列表
     */
    public void cacheActivePositions(List<Position> positions) {
        try {
            redisService.set(
                ACTIVE_POSITIONS_CACHE_KEY, 
                positions, 
                CACHE_EXPIRATION_HOURS, 
                TimeUnit.HOURS
            );
            log.info("Cached {} active positions with {} hours expiration", 
                    positions.size(), CACHE_EXPIRATION_HOURS);
        } catch (Exception e) {
            log.error("Error caching active positions", e);
        }
    }
    
    /**
     * 刷新活跃职位缓存
     */
    public void refreshActivePositionsCache() {
        try {
            log.info("Refreshing active positions cache");
            List<Position> activePositions = positionService.getActivePositions();
            cacheActivePositions(activePositions);
        } catch (Exception e) {
            log.error("Error refreshing active positions cache", e);
        }
    }
    
    /**
     * 清除活跃职位缓存
     */
    public void clearActivePositionsCache() {
        try {
            redisService.delete(ACTIVE_POSITIONS_CACHE_KEY);
            log.info("Cleared active positions cache");
        } catch (Exception e) {
            log.error("Error clearing active positions cache", e);
        }
    }
    
    /**
     * 检查缓存是否存在
     * @return 是否存在缓存
     */
    public boolean isCacheExists() {
        try {
            return redisService.hasKey(ACTIVE_POSITIONS_CACHE_KEY);
        } catch (Exception e) {
            log.error("Error checking cache existence", e);
            return false;
        }
    }
    
    /**
     * 获取缓存剩余过期时间（秒）
     * @return 剩余过期时间，-1表示永不过期，-2表示不存在
     */
    public long getCacheExpiration() {
        try {
            return redisService.getExpire(ACTIVE_POSITIONS_CACHE_KEY);
        } catch (Exception e) {
            log.error("Error getting cache expiration", e);
            return -2;
        }
    }
}