package com.redteamobile.talentsphere.service;

import com.redteamobile.talentsphere.enums.DingTalkNotificationType;
import java.util.Map;
import java.util.List;

public interface DingTalkService {
    
    /**
     * 向面试官发送简历筛选提醒
     * @param interviewerEmail 面试官邮箱
     * @param resumeCount 待筛选简历数量
     * @param positionName 职位名称
     */
    void sendResumeScreeningNotification(String interviewerEmail, int resumeCount, String positionName);
    
    /**
     * 向面试官发送面试安排通知
     * @param interviewerEmail 面试官邮箱
     * @param candidateName 候选人姓名
     * @param positionName 职位名称
     * @param interviewTime 面试时间
     * @param interviewType 面试类型
     * @param location 面试方式
     * @param interviewId 面试ID，用于生成跳转链接
     */
    void sendInterviewScheduledNotification(String interviewerEmail, String candidateName, 
                                          String positionName, String interviewTime, 
                                          String interviewType, String location, Long interviewId);
    
    /**
     * 向面试官发送面试更新通知
     * @param interviewerEmail 面试官邮箱
     * @param candidateName 候选人姓名
     * @param positionName 职位名称
     * @param interviewTime 面试时间
     * @param interviewType 面试类型
     * @param location 面试方式
     * @param interviewId 面试ID，用于生成跳转链接
     */
    void sendInterviewUpdatedNotification(String interviewerEmail, String candidateName, 
                                        String positionName, String interviewTime, 
                                        String interviewType, String location, Long interviewId);
    
    /**
     * 发送自定义消息
     * @param userEmail 用户邮箱
     * @param title 消息标题
     * @param content 消息内容
     */
    void sendCustomMessage(String userEmail, String title, String content);
    
    /**
     * 根据用户名查询DingTalk用户ID
     * @param username 用户名（中文姓名）
     * @return DingTalk用户ID，如果未找到返回null
     */
    String searchUserIdByName(String username);
    
    /**
     * 统一的DingTalk通知发送方法
     * @param type 通知类型
     * @param userEmail 用户邮箱
     * @param params 消息参数
     */
    void sendNotification(DingTalkNotificationType type, String userEmail, Map<String, Object> params);
    
    /**
     * 批量发送DingTalk通知
     * @param type 通知类型
     * @param userEmails 用户邮箱列表
     * @param params 消息参数
     */
    void sendBatchNotification(DingTalkNotificationType type, List<String> userEmails, Map<String, Object> params);
    
    /**
     * 发送面试反馈完成通知
     * @param interviewerEmail 面试官邮箱
     * @param candidateName 候选人姓名
     * @param positionName 职位名称
     * @param interviewTime 面试时间
     * @param result 面试结果
     * @param rating 评分
     * @param interviewId 面试ID
     */
    void sendInterviewFeedbackNotification(String interviewerEmail, String candidateName, 
                                         String positionName, String interviewTime, 
                                         String result, String rating, Long interviewId);
    
    /**
     * 发送面试取消通知
     * @param interviewerEmail 面试官邮箱
     * @param candidateName 候选人姓名
     * @param positionName 职位名称
     * @param interviewTime 面试时间
     * @param interviewType 面试类型
     * @param cancelledBy 取消操作人
     * @param interviewId 面试ID
     */
    void sendInterviewCancelledNotification(String interviewerEmail, String candidateName, 
                                          String positionName, String interviewTime, 
                                          String interviewType, String cancelledBy, Long interviewId);
}