package com.redteamobile.talentsphere.service;

import com.redteamobile.talentsphere.model.ResumePosition;
import java.util.List;

public interface ResumePositionService {
    /**
     * 获取简历关联的职位列表
     */
    List<ResumePosition> getResumePositions(Long resumeId);
    
    /**
     * 更新简历职位关联
     */
    void updateResumePosition(Long resumeId, Long positionId, String status, List<String> screeners);
    
    /**
     * 删除简历职位关联
     */
    void deleteResumePositions(Long resumeId);
} 