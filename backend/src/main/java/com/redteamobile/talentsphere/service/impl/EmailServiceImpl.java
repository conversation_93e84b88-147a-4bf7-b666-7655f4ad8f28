package com.redteamobile.talentsphere.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import com.redteamobile.talentsphere.model.Interview;
import com.redteamobile.talentsphere.service.EmailService;

import io.micrometer.common.util.StringUtils;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class EmailServiceImpl implements EmailService {

    @Autowired
    private JavaMailSender mailSender;
    @Autowired
    private TemplateEngine templateEngine;
    @Autowired
    private Environment environment;

    @Value("classpath:static/images/redtea_logo.png")
    private Resource logoResource;

    @Override
    public void sendInterviewInvitation(String candidateEmail, Interview interview) {
        MimeMessage message = mailSender.createMimeMessage();
        
        try {
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            
            // Add logo as inline image
            helper.addInline("logo", logoResource);

            helper.setFrom(new InternetAddress(environment.getProperty("spring.mail.username"),
                    "RedteaMobile HR Team"));
            
            // Set other email properties
            helper.setTo(candidateEmail);
            for (String cc : interview.getCcList().split(",")) {
                if (StringUtils.isNotEmpty(cc)) {
                    helper.addCc(cc);
                }
            }
            
            helper.setSubject("面试邀请");
            
            Context context = new Context();
            context.setVariable("interview", interview);
            
            String content = templateEngine.process("interview-invitation", context);
            helper.setText(content, true);
            
            mailSender.send(message);
        } catch (MessagingException e) {
            log.error("Failed to send interview invitation email: messaging error", e);
            throw new RuntimeException("Failed to send email", e);
        } catch (java.io.UnsupportedEncodingException e) {
            log.error("Failed to send interview invitation email: encoding error", e);
            throw new RuntimeException("Failed to send email", e);
        }
    }

    @Override
    public void sendPasswordResetEmail(String email, String resetCode) {
        try {
            Context context = new Context();
            context.setVariable("resetCode", resetCode);
            context.setVariable("expiryMinutes", 10);

            String content = templateEngine.process("password-reset", context);

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            helper.setTo(email);
            helper.setSubject("Password Reset Code");
            helper.setText(content, true);

            mailSender.send(message);
        } catch (MessagingException e) {
            throw new RuntimeException("Failed to send password reset email", e);
        }
    }

    @Override
    public void sendAnniversaryEmail(String subject, String email, Context context, int years) {
        try {
            String content = templateEngine.process("anniversary/" + years + "_img_v1.1", context);
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            helper.setTo(email);
            helper.setSubject(subject);
            helper.setText(content, true);
    
            mailSender.send(message);  
        } catch (MessagingException e) {
            throw new RuntimeException("Failed to send anniversary email", e);
        }
    }
} 