package com.redteamobile.talentsphere.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.redteamobile.talentsphere.enums.DingTalkNotificationType;
import com.redteamobile.talentsphere.mapper.UserMapper;
import com.redteamobile.talentsphere.model.User;
import com.redteamobile.talentsphere.service.DingTalkService;

import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class DingTalkServiceImpl implements DingTalkService {
    
    private static final Logger logger = LoggerFactory.getLogger(DingTalkServiceImpl.class);
    
    @Value("${dingtalk.enabled:false}")
    private boolean enabled;
    
    @Value("${dingtalk.app-key}")
    private String appKey;
    
    @Value("${dingtalk.app-secret}")
    private String appSecret;
    
    @Value("${dingtalk.agent-id}")
    private Long agentId;
    
    @Value("${dingtalk.api.base-url:https://oapi.dingtalk.com}")
    private String baseUrl;
    
    @Value("${dingtalk.api.new-base-url:https://api.dingtalk.com}")
    private String newBaseUrl;
    
    @Value("${app.base-url}")
    private String appBaseUrl;
    
    @Autowired
    private UserMapper userMapper;
    
    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // AccessToken缓存
    private final Map<String, TokenInfo> tokenCache = new ConcurrentHashMap<>();
    
    @Override
    @Async
    public void sendResumeScreeningNotification(String interviewerEmail, int resumeCount, String positionName) {
        Map<String, Object> params = new HashMap<>();
        params.put("positionName", positionName);
        params.put("resumeCount", resumeCount);
        params.put("currentTime", LocalDateTime.now(java.time.ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + " (CST)");
        
        sendNotification(DingTalkNotificationType.RESUME_SCREENING, interviewerEmail, params);
    }
    
    @Override
    @Async
    public void sendInterviewScheduledNotification(String interviewerEmail, String candidateName, 
                                                 String positionName, String interviewTime, 
                                                 String interviewType, String location, Long interviewId) {
        Map<String, Object> params = new HashMap<>();
        params.put("candidateName", candidateName);
        params.put("positionName", positionName);
        params.put("interviewTime", interviewTime);
        params.put("location", location);
        params.put("interviewType", interviewType);
        params.put("interviewId", interviewId);
        
        sendNotification(DingTalkNotificationType.INTERVIEW_SCHEDULED, interviewerEmail, params);
    }
    
    @Override
    @Async
    public void sendInterviewUpdatedNotification(String interviewerEmail, String candidateName, 
                                               String positionName, String interviewTime, 
                                               String interviewType, String location, Long interviewId) {
        Map<String, Object> params = new HashMap<>();
        params.put("candidateName", candidateName);
        params.put("positionName", positionName);
        params.put("interviewTime", interviewTime);
        params.put("location", location);
        params.put("interviewType", interviewType);
        params.put("interviewId", interviewId);
        
        sendNotification(DingTalkNotificationType.INTERVIEW_UPDATED, interviewerEmail, params);
    }
    
    @Override
    @Async
    public void sendCustomMessage(String userEmail, String title, String content) {
        if (!enabled) {
            logger.info("DingTalk is disabled, skipping custom message");
            return;
        }
        
        try {
            sendMarkdownMessage(userEmail, title, content);
        } catch (Exception e) {
            logger.error("Failed to send custom message to {}: {}", userEmail, e.getMessage());
        }
    }
    
    /**
     * 统一的DingTalk通知发送方法
     */
    @Async
    public void sendNotification(DingTalkNotificationType type, String userEmail, Map<String, Object> params) {
        if (!enabled) {
            logger.info("DingTalk is disabled, skipping {} notification", type.getTitle());
            return;
        }
        
        try {
            String content = buildNotificationMessage(type, params);
            sendMarkdownMessage(userEmail, type.getTitle(), content);
        } catch (Exception e) {
            logger.error("Failed to send {} notification to {}: {}", type.getTitle(), userEmail, e.getMessage());
        }
    }
    
    /**
     * 批量发送DingTalk通知
     */
    @Async
    public void sendBatchNotification(DingTalkNotificationType type, java.util.List<String> userEmails, Map<String, Object> params) {
        if (!enabled) {
            logger.info("DingTalk is disabled, skipping batch {} notification", type.getTitle());
            return;
        }
        
        userEmails.parallelStream().forEach(email -> {
            try {
                String content = buildNotificationMessage(type, params);
                sendMarkdownMessage(email, type.getTitle(), content);
            } catch (Exception e) {
                logger.error("Failed to send {} notification to {}: {}", type.getTitle(), email, e.getMessage());
            }
        });
    }
    
    /**
     * 构建通知消息内容
     */
    private String buildNotificationMessage(DingTalkNotificationType type, Map<String, Object> params) {
        StringBuilder content = new StringBuilder();
        content.append(type.getFormattedTitle()).append("  \n\n");
        
        switch (type) {
            case RESUME_SCREENING:
                content.append(String.format(
                    "**职位**：%s  \n" +
                    "**待筛选简历数量**：%d份  \n" +
                    "**提醒时间**：%s  \n\n" +
                    "%s  \n\n" +
                    "[点击查看待筛选简历](%s%s)",
                    params.get("positionName"),
                    params.get("resumeCount"),
                    params.get("currentTime"),
                    type.getActionText(),
                    appBaseUrl,
                    type.getUrlTemplate()
                ));
                break;
                
            case RESUME_SHORTLISTED_BATCH:
                content.append(String.format(
                    "**职位**：%s  \n" +
                    "**已筛选通过简历数量**：%d份  \n\n" +
                    "%s  \n\n" +
                    "[点击查看已筛选简历](%s" + type.getUrlTemplate() + ")",
                    params.get("positionName"),
                    params.get("resumeCount"),
                    type.getActionText(),
                    appBaseUrl,
                    params.get("positionId"),
                    params.get("timestamp")
                ));
                break;
                
            case INTERVIEW_SCHEDULED:
            case INTERVIEW_UPDATED:
                content.append(String.format(
                    "**候选人**：%s  \n" +
                    "**职位**：%s  \n" +
                    "**面试时间**：%s  \n" +
                    "**面试方式**：%s  \n" +
                    "**面试类型**：%s  \n\n" +
                    "%s  \n\n" +
                    "[点击查看面试详情](%s" + type.getUrlTemplate() + ")",
                    params.get("candidateName"),
                    params.get("positionName"),
                    params.get("interviewTime"),
                    params.get("location"),
                    params.get("interviewType"),
                    type.getActionText(),
                    appBaseUrl,
                    params.get("interviewId")
                ));
                break;
                
            case INTERVIEW_FEEDBACK:
                content.append(String.format(
                    "**候选人**：%s  \n" +
                    "**职位**：%s  \n" +
                    "**面试官**：%s  \n" +
                    "**面试时间**：%s  \n" +
                    "**面试结果**：%s  \n" +
                    "**评分**：%s  \n\n" +
                    "%s  \n\n" +
                    "[点击查看面试详情](%s" + type.getUrlTemplate() + ")",
                    params.get("candidateName"),
                    params.get("positionName"),
                    params.get("interviewer"),
                    params.get("interviewTime"),
                    params.get("result"),
                    params.get("rating"),
                    type.getActionText(),
                    appBaseUrl,
                    params.get("interviewId")
                ));
                break;
                
            case INTERVIEW_CANCELLED:
                content.append(String.format(
                    "**候选人**：%s  \n" +
                    "**职位**：%s  \n" +
                    "**原定面试时间**：%s  \n" +
                    "**面试类型**：%s  \n" +
                    "**取消操作人**：%s  \n\n" +
                    "%s  \n\n" +
                    "[点击查看详情](%s" + type.getUrlTemplate() + ")",
                    params.get("candidateName"),
                    params.get("positionName"),
                    params.get("interviewTime"),
                    params.get("interviewType"),
                    params.get("cancelledBy"),
                    type.getActionText(),
                    appBaseUrl,
                    params.get("interviewId")
                ));
                break;
        }
        
        return content.toString();
    }
    
    @Override
    @Async
    public void sendInterviewFeedbackNotification(String interviewerEmail, String candidateName, 
                                                String positionName, String interviewTime, 
                                                String result, String rating, Long interviewId) {
        Map<String, Object> params = new HashMap<>();
        params.put("candidateName", candidateName);
        params.put("positionName", positionName);
        params.put("interviewer", interviewerEmail);
        params.put("interviewTime", interviewTime);
        params.put("result", result);
        params.put("rating", rating);
        params.put("interviewId", interviewId);
        
        sendNotification(DingTalkNotificationType.INTERVIEW_FEEDBACK, interviewerEmail, params);
    }
    
    @Override
    @Async
    public void sendInterviewCancelledNotification(String interviewerEmail, String candidateName, 
                                                 String positionName, String interviewTime, 
                                                 String interviewType, String cancelledBy, Long interviewId) {
        Map<String, Object> params = new HashMap<>();
        params.put("candidateName", candidateName);
        params.put("positionName", positionName);
        params.put("interviewTime", interviewTime);
        params.put("interviewType", interviewType);
        params.put("cancelledBy", cancelledBy);
        params.put("interviewId", interviewId);
        
        sendNotification(DingTalkNotificationType.INTERVIEW_CANCELLED, interviewerEmail, params);
    }

    @Override
    public String searchUserIdByName(String username) {
        if (!enabled) {
            logger.info("DingTalk is disabled, skipping user search");
            return null;
        }
        
        try {
            // 获取AccessToken
            String accessToken = getAccessToken();
            if (accessToken == null) {
                logger.error("Failed to get DingTalk access token for user search");
                return null;
            }
            
            // 构建查询参数 - 使用新API格式
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("queryWord", username);
            requestBody.put("offset", 0);
            requestBody.put("size", 10);
            requestBody.put("fullMatchField", 1); // 1表示按姓名全匹配
            
            // 发送请求 - 使用新API端点
            String url = newBaseUrl + "/v1.0/contact/users/search";
            logger.info("Searching DingTalk user with name: {} using new API", username);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("x-acs-dingtalk-access-token", accessToken); // 新API使用Header认证
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                
                // 新API响应格式不同，没有errcode字段
                if (jsonNode.has("list")) {
                    JsonNode userList = jsonNode.get("list");
                    if (userList.isArray() && !userList.isEmpty()) {
                        // 新API返回的是userid字符串数组
                        String userId = userList.get(0).asText();
                        logger.info("Found DingTalk user {} with userid: {}", username, userId);
                        return userId;
                    } else {
                        logger.warn("No user found for name: {}", username);
                    }
                } else {
                    logger.error("Unexpected response format from DingTalk new API: {}", response.getBody());
                }
            } else {
                logger.error("Failed to search DingTalk user, HTTP status: {}", response.getStatusCode());
            }
            
        } catch (Exception e) {
            logger.error("Exception while searching DingTalk user {}: {}", username, e.getMessage(), e);
        }
        
        return null;
    }
    
    /**
     * 发送Markdown格式消息
     */
    private void sendMarkdownMessage(String userEmail, String title, String content) {
        try {
            // 获取用户的DingTalk UserId
            String dingtalkUserId = getDingTalkUserIdByEmail(userEmail);
            if (dingtalkUserId == null) {
                logger.warn("User {} does not have DingTalk UserId configured", userEmail);
                return;
            }
            
            // 获取AccessToken
            String accessToken = getAccessToken();
            if (accessToken == null) {
                logger.error("Failed to get DingTalk access token");
                return;
            }
            
            // 构建消息体
            Map<String, Object> message = new HashMap<>();
            message.put("agent_id", agentId);
            message.put("userid_list", dingtalkUserId);
            
            Map<String, Object> msg = new HashMap<>();
            msg.put("msgtype", "markdown");
            
            Map<String, Object> markdown = new HashMap<>();
            markdown.put("title", title);
            markdown.put("text", content);
            msg.put("markdown", markdown);
            
            message.put("msg", msg);
            
            // 发送请求
            String url = baseUrl + "/topapi/message/corpconversation/asyncsend_v2?access_token=" + accessToken;
            logger.info("Sending DingTalk message to {} with URL: {}", userEmail, url);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(message, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
            logger.info("DingTalk API response: {}", response.getBody());
            
            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                int errcode = jsonNode.get("errcode").asInt();
                if (errcode == 0) {
                    logger.info("Successfully sent DingTalk message to {}", userEmail);
                } else {
                    logger.error("DingTalk API error: {} - {}", errcode, jsonNode.get("errmsg").asText());
                }
            } else {
                logger.error("Failed to send DingTalk message, HTTP status: {}", response.getStatusCode());
            }
        } catch (Exception e) {
            logger.error("Exception while sending DingTalk message to {}: {}", userEmail, e.getMessage(), e);
        }
    }
    
    /**
     * 获取AccessToken
     */
    private String getAccessToken() {
        try {
            // 检查缓存
            TokenInfo tokenInfo = tokenCache.get("access_token");
            if (tokenInfo != null && !tokenInfo.isExpired()) {
                return tokenInfo.getToken();
            }
            
            // 获取新的AccessToken
            String url = baseUrl + "/gettoken?appkey=" + appKey + "&appsecret=" + appSecret;
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                int errcode = jsonNode.get("errcode").asInt();
                if (errcode == 0) {
                    String accessToken = jsonNode.get("access_token").asText();
                    long expiresIn = jsonNode.get("expires_in").asLong();
                    
                    // 缓存token（提前5分钟过期）
                    long expireTime = System.currentTimeMillis() + (expiresIn - 300) * 1000;
                    tokenCache.put("access_token", new TokenInfo(accessToken, expireTime));
                    
                    logger.info("Successfully obtained DingTalk access token");
                    return accessToken;
                } else {
                    logger.error("Failed to get DingTalk access token: {} - {}", errcode, jsonNode.get("errmsg").asText());
                }
            }
        } catch (Exception e) {
            logger.error("Exception while getting DingTalk access token: {}", e.getMessage(), e);
        }
        return null;
    }
    
    /**
     * 根据邮箱获取DingTalk UserId
     */
    private String getDingTalkUserIdByEmail(String email) {
        try {
            User user = userMapper.findByEmail(email);
            return user != null ? user.getDingtalkUserid() : null;
        } catch (Exception e) {
            logger.error("Failed to get DingTalk UserId for email {}: {}", email, e.getMessage());
            return null;
        }
    }
    
    /**
     * Token信息内部类
     */
    private static class TokenInfo {
        @Getter
        private final String token;
        private final long expireTime;
        
        public TokenInfo(String token, long expireTime) {
            this.token = token;
            this.expireTime = expireTime;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() >= expireTime;
        }
    }
}