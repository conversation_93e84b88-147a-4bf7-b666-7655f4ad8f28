package com.redteamobile.talentsphere.service;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.redteamobile.talentsphere.dto.PageResponse;
import com.redteamobile.talentsphere.model.StaffInfo;

public interface StaffService {
    /**
     * Get all staff information
     * @return List of StaffInfo
     */
    List<StaffInfo> getAllStaff();
    
    /**
     * Get paginated staff information
     * @param page Page number (1-based)
     * @param size Page size
     * @return PageResponse containing paginated data and pagination info
     */
    PageResponse<StaffInfo> getAllStaffPaginated(int page, int size);
    
    /**
     * Import staff information from Excel file
     * @param file Excel file
     * @return true if import successful
     */
    boolean importStaff(MultipartFile file);
    
    /**
     * Send anniversary blessing email to a staff member
     * @param staffId staff ID
     * @return true if sending successful
     */
    boolean sendBlessing(String staffId);
    
    /**
     * Check anniversaries and send blessing emails automatically
     * This method is scheduled to run at midnight every day
     */
    void checkAndSendAnniversaryEmails();

    /*
     * Download blessing image as byte array
     * @param token JWT token
     * @return byte array of blessing image
     */
    byte[] downloadAsImage(String token);
    
    /**
     * Delete staff information by staff ID
     * @param staffId staff ID
     * @return true if deletion successful
     */
    boolean deleteStaff(String staffId);
} 