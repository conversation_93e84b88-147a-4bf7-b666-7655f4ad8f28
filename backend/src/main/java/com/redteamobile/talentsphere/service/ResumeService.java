package com.redteamobile.talentsphere.service;

import com.redteamobile.talentsphere.dto.PageResponse;
import com.redteamobile.talentsphere.model.Resume;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

public interface ResumeService {
    Resume importResume(MultipartFile file, String source, Long positionId, List<String> screeners);
    Resume getResume(Long id);
    PageResponse<Resume> getResumesByStatus(String status, int page, int pageSize, String search);
    PageResponse<Resume> getAllResumes(int page, int pageSize, String search);
    PageResponse<Resume> getAllResumes(int page, int pageSize, String search, List<String> status, List<Long> positionId);
    PageResponse<Resume> getResumesByPosition(Long positionId, int page, int pageSize, String search);
    void updateResumeStatus(Long id, String status);
    int updateResume(Resume resume, String source);
    void deleteResume(Long id);
    
    // 人才库相关方法
    void moveToTalentPool(Long resumeId);
    PageResponse<Resume> getTalentPoolResumes(int page, int pageSize, String search);
    void moveFromTalentPool(Long resumeId, Long positionId, List<String> screeners);
} 