package com.redteamobile.talentsphere.service;

import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.seg.Segment;
import com.hankcs.hanlp.seg.common.Term;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class ResumeParserService {
    private static final Logger logger = LoggerFactory.getLogger(ResumeParserService.class);

    // Regular expressions for contact information
    private static final Pattern EMAIL_PATTERN = Pattern.compile("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,6}");
    private static final Pattern PHONE_PATTERN = Pattern.compile("(?:(?:\\+|00)86)?1[3-9]\\d{9}");

    public Map<String, String> parseResume(String text) {
        try {
            // Parse the extracted text
            Map<String, String> result = new HashMap<>();

            // Extract contact information using regex
            result.put("email", extractEmail(text));
            result.put("phone_number", extractPhone(text));

            // Use HanLP for name extraction
            result.put("name", extractName(text));

            logger.info("Resume parsed successfully: {}", result);
            return result;

        } catch (Exception e) {
            logger.error("Failed to parse resume", e);
            throw new RuntimeException("Failed to parse resume", e);
        }
    }

    private String extractEmail(String text) {
        Matcher matcher = EMAIL_PATTERN.matcher(text);
        String email = "";
        if (matcher.find()) {
            email = matcher.group();
            System.out.println("Resume email: " + email);
        }
        return email;
    }

    private String extractPhone(String text) {
        Matcher matcher = PHONE_PATTERN.matcher(text);
        String phoneNum = "";
        if (matcher.find()) {
            phoneNum = matcher.group();
            System.out.println("Resume phone number: " + phoneNum);
        }
        return phoneNum;
    }

    private String extractName(String text) {
        try {
            Segment segment = HanLP.newSegment()
                    .enablePartOfSpeechTagging(true)
                    .enableNameRecognize(true)
                    .enablePlaceRecognize(true)
                    .enableOrganizationRecognize(true)
                    .enableCustomDictionary(false)
                    .enableTranslatedNameRecognize(false)
                    .enableOffset(true);
            // Use HanLP to segment text and identify names
            List<Term> terms = segment.seg(text);

            // Look for terms tagged as names (nr)
            for (Term term : terms) {
                if (term.nature.startsWith("nr")) {
                    System.out.println("Resume name:" + term.word);
                    return term.word;
                }
            }

            return "";
        } catch (Exception e) {
            logger.warn("Failed to extract name", e);
            return "";
        }
    }
}