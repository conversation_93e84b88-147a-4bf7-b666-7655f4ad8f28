package com.redteamobile.talentsphere.exception;

import com.redteamobile.talentsphere.dto.InterviewValidationResult;

import java.util.List;

public class InterviewSchedulingException extends RuntimeException {
    private final InterviewValidationResult.InterviewInfo blockingInterview;
    
    public InterviewSchedulingException(String message, InterviewValidationResult.InterviewInfo blockingInterview) {
        super(message);
        this.blockingInterview = blockingInterview;
    }
    
    public InterviewValidationResult.InterviewInfo getBlockingInterview() {
        return blockingInterview;
    }
}
