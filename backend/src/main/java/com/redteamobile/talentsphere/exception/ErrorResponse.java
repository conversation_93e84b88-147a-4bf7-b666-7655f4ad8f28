package com.redteamobile.talentsphere.exception;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ErrorResponse {
    private String error;
    private String message;
    private Map<String, Object> data;
    
    public ErrorResponse(String error, String message) {
        this.error = error;
        this.message = message;
    }
} 