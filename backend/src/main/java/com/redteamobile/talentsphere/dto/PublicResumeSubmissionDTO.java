package com.redteamobile.talentsphere.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Data
public class PublicResumeSubmissionDTO {
    
    private String firstName;
    private String lastName;
    private String email;
    private String phone;
    private String countryCode;
    private Long positionId;
    private MultipartFile resume;
    private Boolean privacyAccepted;
    
    // Helper method to get full name based on country code
    public String getFullName() {
        // For China and Chinese regions, return lastName + firstName
        if (countryCode != null && (countryCode.startsWith("+86") || 
                countryCode.equals("+852") || countryCode.equals("+853") || 
                countryCode.equals("+886"))) {
            return lastName + firstName;
        }
        // For other countries, return firstName + lastName
        return firstName + " " + lastName;
    }
    
    // Helper method to get full phone number
    public String getFullPhoneNumber() {
        return countryCode + phone;
    }
}