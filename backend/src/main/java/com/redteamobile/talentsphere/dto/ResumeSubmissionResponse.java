package com.redteamobile.talentsphere.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ResumeSubmissionResponse {
    private boolean success;
    private String message;
    private Long resumeId;
    
    // Constructor for success without resumeId
    public ResumeSubmissionResponse(boolean success, String message) {
        this.success = success;
        this.message = message;
        this.resumeId = null;
    }
}