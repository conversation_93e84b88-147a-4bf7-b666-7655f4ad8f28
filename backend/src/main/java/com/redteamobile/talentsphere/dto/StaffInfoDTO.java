package com.redteamobile.talentsphere.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StaffInfoDTO {
    private Long id;
    private String staffId;
    private String name;
    private String email;
    private Boolean gender;
    private LocalDateTime startDate;
    private Boolean employStatus;
    private LocalDateTime createdDate;
    private LocalDateTime updatedDate;
} 