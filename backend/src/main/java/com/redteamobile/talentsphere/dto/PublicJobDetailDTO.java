package com.redteamobile.talentsphere.dto;

import com.redteamobile.talentsphere.enums.HireSourceTypeEnum;
import lombok.Data;
import java.time.LocalDateTime;

@Data
public class PublicJobDetailDTO {
    private final Long id;
    private final String title;
    private final String level;
    private final String workplace;
    private final String description;
    private final String requirements;
    private final HireSourceTypeEnum hireSourceType;
    private final LocalDateTime updatedAt;
    
    public PublicJobDetailDTO(Long id, String title, String level, 
                             String workplace, String description, String requirements,
                             HireSourceTypeEnum hireSourceType, LocalDateTime updatedAt) {
        this.id = id;
        this.title = title;
        this.level = level;
        this.workplace = workplace;
        this.description = description;
        this.requirements = requirements;
        this.hireSourceType = hireSourceType;
        this.updatedAt = updatedAt;
    }
}

