package com.redteamobile.talentsphere.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InterviewValidationResult {
    private boolean canSchedule;
    private String message;
    private InterviewInfo blockingInterview;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InterviewInfo {
        private Long id;
        private String interviewType;
        private LocalDateTime scheduledTime;
        private String interviewer;
        private String status;
        private String interviewUrl;
    }
}
