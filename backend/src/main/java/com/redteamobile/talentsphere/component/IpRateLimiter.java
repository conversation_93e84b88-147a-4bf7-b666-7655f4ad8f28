package com.redteamobile.talentsphere.component;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class IpRateLimiter {
    
    // IP访问记录 - IP -> 访问信息
    private final ConcurrentHashMap<String, IpAccessInfo> ipAccessMap = new ConcurrentHashMap<>();
    
    // 配置参数
    private static final int MAX_REQUESTS_PER_MINUTE = 60; // 每分钟最多60次请求
    private static final int MAX_REQUESTS_PER_HOUR = 300;  // 每小时最多300次请求
    private static final int MAX_SUBMISSIONS_PER_DAY = 5;  // 每天最多5次简历提交
    
    /**
     * 检查IP是否被限流（一般请求）
     */
    public boolean isAllowed(String ipAddress) {
        IpAccessInfo accessInfo = ipAccessMap.computeIfAbsent(ipAddress, k -> new IpAccessInfo());
        LocalDateTime now = LocalDateTime.now();
        
        // 检查每分钟限制
        if (accessInfo.getRequestsInMinute(now) >= MAX_REQUESTS_PER_MINUTE) {
            log.warn("IP {} exceeded minute rate limit. Requests in last minute: {}", 
                    ipAddress, accessInfo.getRequestsInMinute(now));
            return false;
        }
        
        // 检查每小时限制
        if (accessInfo.getRequestsInHour(now) >= MAX_REQUESTS_PER_HOUR) {
            log.warn("IP {} exceeded hour rate limit. Requests in last hour: {}", 
                    ipAddress, accessInfo.getRequestsInHour(now));
            return false;
        }
        
        // 记录访问
        accessInfo.recordAccess(now);
        return true;
    }
    
    /**
     * 检查IP是否允许简历提交（更严格的限制）
     */
    public boolean isSubmissionAllowed(String ipAddress) {
        if (!isAllowed(ipAddress)) {
            return false;
        }
        
        IpAccessInfo accessInfo = ipAccessMap.get(ipAddress);
        LocalDateTime now = LocalDateTime.now();
        
        // 检查每天简历提交限制
        if (accessInfo.getSubmissionsInDay(now) >= MAX_SUBMISSIONS_PER_DAY) {
            log.warn("IP {} exceeded daily submission limit. Submissions in last day: {}", 
                    ipAddress, accessInfo.getSubmissionsInDay(now));
            return false;
        }
        
        // 记录简历提交
        accessInfo.recordSubmission(now);
        return true;
    }
    
    /**
     * 定时清理过期记录 - 每天零点执行
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void cleanupExpiredRecords() {
        LocalDateTime now = LocalDateTime.now();
        int removedCount = 0;
        
        var iterator = ipAccessMap.entrySet().iterator();
        while (iterator.hasNext()) {
            var entry = iterator.next();
            if (entry.getValue().isExpired(now)) {
                iterator.remove();
                removedCount++;
            }
        }
        
        log.info("Daily cleanup completed. Removed {} expired IP records at {}", removedCount, now);
    }
    
    /**
     * IP访问信息类
     */
    private static class IpAccessInfo {
        private final ConcurrentHashMap<LocalDateTime, AtomicInteger> accessLog = new ConcurrentHashMap<>();
        private final ConcurrentHashMap<LocalDateTime, AtomicInteger> submissionLog = new ConcurrentHashMap<>();
        
        public void recordAccess(LocalDateTime time) {
            // 记录到分钟级别
            LocalDateTime minuteKey = time.truncatedTo(ChronoUnit.MINUTES);
            accessLog.computeIfAbsent(minuteKey, k -> new AtomicInteger(0)).incrementAndGet();
        }
        
        public void recordSubmission(LocalDateTime time) {
            // 记录到小时级别
            LocalDateTime hourKey = time.truncatedTo(ChronoUnit.HOURS);
            submissionLog.computeIfAbsent(hourKey, k -> new AtomicInteger(0)).incrementAndGet();
        }
        
        public int getRequestsInMinute(LocalDateTime now) {
            LocalDateTime oneMinuteAgo = now.minus(1, ChronoUnit.MINUTES);
            return accessLog.entrySet().stream()
                    .filter(entry -> entry.getKey().isAfter(oneMinuteAgo))
                    .mapToInt(entry -> entry.getValue().get())
                    .sum();
        }
        
        public int getRequestsInHour(LocalDateTime now) {
            LocalDateTime oneHourAgo = now.minus(1, ChronoUnit.HOURS);
            return accessLog.entrySet().stream()
                    .filter(entry -> entry.getKey().isAfter(oneHourAgo))
                    .mapToInt(entry -> entry.getValue().get())
                    .sum();
        }
        
        public int getSubmissionsInDay(LocalDateTime now) {
            LocalDateTime oneDayAgo = now.minus(1, ChronoUnit.DAYS);
            return submissionLog.entrySet().stream()
                    .filter(entry -> entry.getKey().isAfter(oneDayAgo))
                    .mapToInt(entry -> entry.getValue().get())
                    .sum();
        }
        
        public boolean isExpired(LocalDateTime now) {
            // 如果最后一次访问超过24小时，则认为过期
            LocalDateTime oneDayAgo = now.minus(1, ChronoUnit.DAYS);
            
            boolean hasRecentAccess = accessLog.keySet().stream()
                    .anyMatch(time -> time.isAfter(oneDayAgo));
            boolean hasRecentSubmission = submissionLog.keySet().stream()
                    .anyMatch(time -> time.isAfter(oneDayAgo));
            
            return !hasRecentAccess && !hasRecentSubmission;
        }
    }
}