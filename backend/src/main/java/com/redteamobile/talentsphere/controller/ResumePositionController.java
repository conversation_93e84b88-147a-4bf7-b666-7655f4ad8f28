package com.redteamobile.talentsphere.controller;

import com.redteamobile.talentsphere.model.ResumePosition;
import com.redteamobile.talentsphere.service.ResumePositionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/resume-positions")
public class ResumePositionController {

    @Autowired
    private ResumePositionService resumePositionService;

    @GetMapping("/resume/{resumeId}")
    public ResponseEntity<List<ResumePosition>> getResumePositions(@PathVariable Long resumeId) {
        return ResponseEntity.ok(resumePositionService.getResumePositions(resumeId));
    }

    @PostMapping
    public ResponseEntity<Void> updateResumePosition(@RequestBody ResumePosition resumePosition) {
        resumePositionService.updateResumePosition(resumePosition.getResumeId(),
                resumePosition.getPositionId(), resumePosition.getStatus(), resumePosition.getScreeners());
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/resume/{resumeId}")
    public ResponseEntity<Void> deleteResumePositions(@PathVariable Long resumeId) {
        resumePositionService.deleteResumePositions(resumeId);
        return ResponseEntity.ok().build();
    }
} 