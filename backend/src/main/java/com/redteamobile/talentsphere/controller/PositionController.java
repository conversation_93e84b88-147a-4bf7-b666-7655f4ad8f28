package com.redteamobile.talentsphere.controller;

import com.redteamobile.talentsphere.model.Position;
import com.redteamobile.talentsphere.service.PositionService;
import com.redteamobile.talentsphere.service.ContentCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Controller for managing job positions
 */
@RestController
@RequestMapping("/api/positions")
public class PositionController {

    @Autowired
    private PositionService positionService;
    
    @Autowired
    private ContentCacheService contentCacheService;

    @GetMapping
    public ResponseEntity<List<Position>> getPositions() {
        return ResponseEntity.ok(positionService.getPositions());
    }

    /**
     * Create a new job position
     */
    @PostMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'HR')")
    public ResponseEntity<Position> createPosition(@RequestBody Position position) {
        Position createdPosition = positionService.createPosition(position);
        // 刷新缓存
        contentCacheService.refreshActivePositionsCache();
        return ResponseEntity.ok(createdPosition);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HR')")
    public ResponseEntity<Void> updateStatus(@PathVariable Long id,
                                             @RequestBody Position position) {
        positionService.updatePosition(position);
        // 刷新缓存
        contentCacheService.refreshActivePositionsCache();
        return ResponseEntity.ok().build();
    }

    /**
     * Get all active positions
     */
    @GetMapping("/active")
    public ResponseEntity<List<Position>> getActivePositions() {
        return ResponseEntity.ok(positionService.getActivePositions());
    }

    @GetMapping("/resume/{resumeId}")
    public ResponseEntity<List<Position>> getPositionsByResumeId(@PathVariable Long resumeId) {
        return ResponseEntity.ok(positionService.getPositionsByResumeId(resumeId));
    }

} 