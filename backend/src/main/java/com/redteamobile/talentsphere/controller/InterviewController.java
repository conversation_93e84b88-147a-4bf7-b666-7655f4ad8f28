package com.redteamobile.talentsphere.controller;

import com.redteamobile.talentsphere.dto.InterviewScheduleRequest;
import com.redteamobile.talentsphere.dto.InterviewUpdateRequest;
import com.redteamobile.talentsphere.dto.PageResponse;
import com.redteamobile.talentsphere.dto.InterviewValidationResult;
import com.redteamobile.talentsphere.model.Interview;
import com.redteamobile.talentsphere.service.InterviewService;
import com.redteamobile.talentsphere.service.RedisService;
import com.redteamobile.talentsphere.model.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Controller for managing interview operations
 */
@RestController
@RequestMapping("/api/interviews")
public class InterviewController {

    @Autowired
    private InterviewService interviewService;
    
    @Autowired
    private RedisService redisService;
    
    private static final String CONTACT_HISTORY_KEY = "hr_shared_contact_history";
    private static final int MAX_HISTORY_SIZE = 20;

    @GetMapping()
    public ResponseEntity<PageResponse<Interview>> getInterviews(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) List<Long> positionIds,
            @RequestParam(required = false) List<String> statuses) {
        return ResponseEntity.ok(interviewService.getInterviews(page, pageSize, startDate, endDate, search, positionIds, statuses));
    }

    /**
     * Get interview by id
     *
     * @param id The interview ID
     * @return The interview with the given ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<Interview> getInterviewById(@PathVariable Long id) {
        Interview interview = interviewService.getInterviewById(id);
        return ResponseEntity.ok(interview);
    }

    /**
     * Schedule a new interview
     * Creates interview record and generates QR code for check-in
     * 
     * @param request Interview details including type, time, and interviewer
     * @return The created Interview object with QR code
     */
    @PostMapping("/schedule")
    @PreAuthorize("hasAnyRole('ADMIN', 'HR')")
    public ResponseEntity<Interview> scheduleInterview(
            @RequestBody InterviewScheduleRequest request
    ) {
        Interview interview = interviewService.scheduleInterview(
            request.getPositionId(),
            request.getResumeId(),
            request.getCandidate(),
            request.getCandidateEmail(),
            request.getInterviewer(),
            request.getContact(),
            request.getCc(),
            request.getScheduledTime(),
            request.getInterviewType(),
            request.isNotifyCandidate(),
            request.getRemark(),
            request.getLocation()
        );
        return ResponseEntity.ok(interview);
    }

    /**
     * Get all interviews for a specific resume
     * 
     * @param resumeId The resume ID to get interviews for
     * @return List of all interviews scheduled for the resume
     */
    @GetMapping("/resume/{resumeId}")
    public ResponseEntity<List<Interview>> getInterviewsByResume(@PathVariable Long resumeId) {
        return ResponseEntity.ok(interviewService.getInterviewsByResumeId(resumeId));
    }

    /**
     * Get previous completed interviews for a resume
     *
     * @param resumeId The resume ID to get feedback history for
     * @param currentTime The current interview time (to get only previous interviews)
     * @return List of completed interviews with feedback
     */
    @GetMapping("/resume/{resumeId}/feedback-history")
    public ResponseEntity<List<Interview>> getInterviewFeedbackHistory(
            @PathVariable Long resumeId,
            @RequestParam String currentTime) {
        return ResponseEntity.ok(interviewService.getInterviewFeedbackHistory(resumeId, currentTime));
    }

    /**
     * Validate if a new interview can be scheduled for a resume
     *
     * @param resumeId The resume ID to validate
     * @return Validation result with blocking interviews if any
     */
    @GetMapping("/resume/{resumeId}/validate-scheduling")
    @PreAuthorize("hasAnyRole('ADMIN', 'HR')")
    public ResponseEntity<InterviewValidationResult> validateScheduling(@PathVariable Long resumeId) {
        InterviewValidationResult result = interviewService.validateNewInterviewScheduling(resumeId);
        return ResponseEntity.ok(result);
    }

    /**
     * Submit interview feedback and rating
     * 
     * @param interviewId The interview to update
     * @param rating Numerical rating (1-5)
     * @param feedback Detailed feedback from interviewer
     * @return Empty response with OK status
     */
    @PostMapping("/{interviewId}/feedback")
    public ResponseEntity<Void> provideFeedback(
            @PathVariable Long interviewId,
            @RequestParam Float rating,
            @RequestParam String result,
            @RequestParam String feedback) {
        interviewService.updateInterviewFeedback(interviewId, rating, result, feedback);
        return ResponseEntity.ok().build();
    }

    /**
     * Record candidate check-in for interview
     * Updates interview status when candidate scans QR code
     * 
     * @param interviewId The interview to check in for
     * @return Empty response with OK status
     */
    @PostMapping("/{interviewId}/checkin")
    public ResponseEntity<Void> checkin(@PathVariable Long interviewId) {
        interviewService.recordCheckin(interviewId);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/status/{status}")
    public ResponseEntity<PageResponse<Interview>> getInterviewsByStatus(
            @PathVariable String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) List<Long> positionIds) {
        return ResponseEntity.ok(interviewService.getInterviewsByStatus(status, page, pageSize, startDate, endDate, search, positionIds));
    }

    /**
     * Cancel an interview
     * Updates interview status to CANCELLED
     * 
     * @param id The interview ID to cancel
     * @return The updated Interview object
     */
    @PatchMapping("/{id}/cancel")
    public ResponseEntity<Interview> cancelInterview(@PathVariable Long id) {
        Interview interview = interviewService.cancelInterview(id);
        return ResponseEntity.ok(interview);
    }

    /**
     * Update an existing interview
     * 
     * @param id The interview ID to update
     * @param request Updated interview details
     * @return The updated Interview object
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HR')")
    public ResponseEntity<Interview> updateInterview(
            @PathVariable Long id,
            @RequestBody InterviewUpdateRequest request
    ) {
        Interview interview = interviewService.updateInterview(
            id,
            request.getInterviewer(),
            request.getContact(),
            request.getCc(),
            request.getScheduledTime(),
            request.getInterviewType(),
            request.isNotifyCandidate(),
            request.getRemark(),
            request.getLocation(),
            request.getPositionId()
        );
        return ResponseEntity.ok(interview);
    }

    /**
     * 获取HR共享的联系方式历史记录
     * 
     * @return 联系方式列表
     */
    @GetMapping("/contact-history")
    @PreAuthorize("hasAnyRole('ADMIN', 'HR')")
    public ResponseEntity<List<String>> getContactHistory() {
        try {
            Set<String> contactSet = redisService.sMembers(CONTACT_HISTORY_KEY);
            List<String> contactHistory = contactSet != null ? new ArrayList<>(contactSet) : new ArrayList<>();
            return ResponseEntity.ok(contactHistory);
        } catch (Exception e) {
            // Redis失败时返回空列表
            return ResponseEntity.ok(new ArrayList<>());
        }
    }

    /**
     * 添加新的联系方式到HR共享历史记录
     * 
     * @param request 包含联系方式的请求体
     * @return 成功响应
     */
    @PostMapping("/contact-history")
    @PreAuthorize("hasAnyRole('ADMIN', 'HR')")
    public ResponseEntity<Map<String, String>> addContactInfo(@RequestBody Map<String, String> request) {
        String contactInfo = request.get("contactInfo");
        
        if (!StringUtils.hasText(contactInfo)) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "联系方式不能为空");
            return ResponseEntity.badRequest().body(errorResponse);
        }
        
        try {
            // 添加到共享的Redis Set（永久保存）
            redisService.sAdd(CONTACT_HISTORY_KEY, contactInfo.trim());
            
            // 限制历史记录数量
            Set<String> allContacts = redisService.sMembers(CONTACT_HISTORY_KEY);
            if (allContacts != null && allContacts.size() > MAX_HISTORY_SIZE) {
                // 如果超过限制，删除一些旧记录（简单处理：删除整个key重新添加最近的）
                List<String> contactList = new ArrayList<>(allContacts);
                redisService.delete(CONTACT_HISTORY_KEY);
                
                // 保留最新的记录
                int startIndex = Math.max(0, contactList.size() - MAX_HISTORY_SIZE);
                for (int i = startIndex; i < contactList.size(); i++) {
                    redisService.sAdd(CONTACT_HISTORY_KEY, contactList.get(i));
                }
            }
        } catch (Exception e) {
            // Redis失败时忽略错误
        }
        
        Map<String, String> response = new HashMap<>();
        response.put("message", "联系方式已添加");
        return ResponseEntity.ok(response);
    }

    /**
     * 从HR共享历史记录中删除联系方式
     * 
     * @param request 包含要删除的联系方式的请求体
     * @return 成功响应
     */
    @DeleteMapping("/contact-history")
    @PreAuthorize("hasAnyRole('ADMIN', 'HR')")
    public ResponseEntity<Map<String, String>> removeContactInfo(@RequestBody Map<String, String> request) {
        String contactInfo = request.get("contactInfo");
        
        if (!StringUtils.hasText(contactInfo)) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "联系方式不能为空");
            return ResponseEntity.badRequest().body(errorResponse);
        }
        
        try {
            // 从共享的Redis Set中删除
            Set<String> allContacts = redisService.sMembers(CONTACT_HISTORY_KEY);
            if (allContacts != null && allContacts.contains(contactInfo.trim())) {
                redisService.delete(CONTACT_HISTORY_KEY);
                for (String contact : allContacts) {
                    if (!contact.equals(contactInfo.trim())) {
                        redisService.sAdd(CONTACT_HISTORY_KEY, contact);
                    }
                }
            }
        } catch (Exception e) {
            // Redis失败时忽略错误
        }
        
        Map<String, String> response = new HashMap<>();
        response.put("message", "联系方式已删除");
        return ResponseEntity.ok(response);
    }

    /**
     * 清空HR共享的联系方式历史记录
     * 注意：联系方式历史记录永久保存，只能通过此接口主动清理
     * 
     * @return 成功响应
     */
    @DeleteMapping("/contact-history/all")
    @PreAuthorize("hasAnyRole('ADMIN', 'HR')")
    public ResponseEntity<Map<String, String>> clearContactHistory() {
        try {
            redisService.delete(CONTACT_HISTORY_KEY);
        } catch (Exception e) {
            // Redis失败时忽略错误
        }
        
        Map<String, String> response = new HashMap<>();
        response.put("message", "联系方式历史已清空");
        return ResponseEntity.ok(response);
    }

    /**
     * 获取当前用户
     */
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return (User) authentication.getPrincipal();
    }
} 