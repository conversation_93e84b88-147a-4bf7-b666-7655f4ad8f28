package com.redteamobile.talentsphere.controller;

import com.redteamobile.talentsphere.model.ResumeScreening;
import com.redteamobile.talentsphere.service.ResumeScreeningService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Controller for managing resume screening operations
 */
@RestController
@RequestMapping("/api/screenings")
public class ResumeScreeningController {

    @Autowired
    private ResumeScreeningService screeningService;

    /**
     * Screen a resume against job requirements
     * Performs automated matching and skill analysis
     * 
     * @param resumeId The ID of the resume to screen
     * @param jobRequirements Comma-separated list of required skills and qualifications
     * @return The screening result with matching score and analysis
     */
    @PostMapping("/resume/{resumeId}")
    public ResponseEntity<ResumeScreening> screenResume(
            @PathVariable Long resumeId,
            @RequestParam String jobRequirements) {
        return ResponseEntity.ok(screeningService.screenResume(resumeId, jobRequirements));
    }

    /**
     * Get all screening results for a specific resume
     * 
     * @param resumeId The resume ID to get screenings for
     * @return List of all screening results for the resume
     */
    @GetMapping("/resume/{resumeId}")
    public ResponseEntity<List<ResumeScreening>> getScreenings(@PathVariable Long resumeId) {
        return ResponseEntity.ok(screeningService.getScreeningsByResumeId(resumeId));
    }

    /**
     * Update the screening result and add notes
     * Used by HR to override automated screening results
     * 
     * @param screeningId The screening record to update
     * @param result New screening result (PASSED/FAILED/PENDING_REVIEW)
     * @param notes Optional notes explaining the decision
     * @return Empty response with OK status
     */
    @PutMapping("/{screeningId}")
    public ResponseEntity<Void> updateScreeningResult(
            @PathVariable Long screeningId,
            @RequestParam String result,
            @RequestParam(required = false) String notes) {
        screeningService.updateScreeningResult(screeningId, result, notes);
        return ResponseEntity.ok().build();
    }

    /**
     * Quick preview screening of a resume by interviewer
     * Allows quick pass/fail decision without detailed screening
     * 
     * @param resumeId The ID of the resume to preview
     * @param isPass True if resume passes preview, false otherwise
     * @return The preview screening result
     */
    @PostMapping("/resume/{resumeId}/preview")
    public ResponseEntity<ResumeScreening> previewResume(
            @PathVariable Long resumeId,
            @RequestParam boolean isPass) {
        ResumeScreening screening = screeningService.previewResume(resumeId, isPass);
        return ResponseEntity.ok(screening);
    }
} 