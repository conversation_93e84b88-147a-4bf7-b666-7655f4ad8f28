package com.redteamobile.talentsphere.controller;

import com.redteamobile.talentsphere.dto.StatisticResponse;
import com.redteamobile.talentsphere.service.StatisticService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Map;

@RestController
@RequestMapping("/api/statistics")
public class StatisticController {

    @Autowired
    private StatisticService statisticService;

    @GetMapping("/resumes/status")
    public ResponseEntity<Map<String, Long>> getResumeStatusCount() {
        return ResponseEntity.ok(statisticService.getResumeStatusCount());
    }

    @GetMapping("/resumes/trend")
    public ResponseEntity<Map<LocalDate, Long>> getResumeUploadTrend(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return ResponseEntity.ok(statisticService.getResumeUploadTrend(startDate, endDate));
    }

    @GetMapping("/interviews/status")
    public ResponseEntity<Map<String, Long>> getInterviewStatusCount() {
        return ResponseEntity.ok(statisticService.getInterviewStatusCount());
    }

    @GetMapping("/interviews/trend")
    public ResponseEntity<Map<LocalDate, Long>> getInterviewTrend(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return ResponseEntity.ok(statisticService.getInterviewTrend(startDate, endDate));
    }

    @GetMapping("/overview")
    public ResponseEntity<StatisticResponse> getOverview() {
        return ResponseEntity.ok(statisticService.getOverview());
    }
} 