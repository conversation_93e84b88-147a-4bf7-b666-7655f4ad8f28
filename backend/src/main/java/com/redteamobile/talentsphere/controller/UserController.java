package com.redteamobile.talentsphere.controller;

import com.redteamobile.talentsphere.dto.UserSummary;
import com.redteamobile.talentsphere.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
public class UserController {
    
    private final UserService userService;
    
    /**
     * 根据邮箱模糊搜索用户
     */
    @GetMapping("/search")
    public ResponseEntity<List<UserSummary>> searchUsers(
            @RequestParam String query,
            @RequestParam(defaultValue = "10") int limit) {
        
        // 验证查询参数
        if (query == null || query.trim().length() < 2) {
            return ResponseEntity.badRequest().build();
        }
        
        // 限制查询长度和结果数量
        String trimmedQuery = query.trim();
        if (trimmedQuery.length() > 50) {
            trimmedQuery = trimmedQuery.substring(0, 50);
        }
        
        if (limit > 20) {
            limit = 20;
        }
        
        List<UserSummary> users = userService.searchUsersByEmail(trimmedQuery, limit);
        return ResponseEntity.ok(users);
    }
}