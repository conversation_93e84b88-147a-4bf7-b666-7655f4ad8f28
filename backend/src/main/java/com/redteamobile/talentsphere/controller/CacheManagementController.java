package com.redteamobile.talentsphere.controller;

import com.redteamobile.talentsphere.service.ContentCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 缓存管理控制器 - 用于管理员监控和管理缓存
 */
@RestController
@RequestMapping("/api/admin/cache")
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("hasRole('ADMIN')")
public class CacheManagementController {
    
    private final ContentCacheService contentCacheService;
    
    /**
     * 获取职位缓存状态
     */
    @GetMapping("/positions/status")
    public ResponseEntity<Map<String, Object>> getPositionCacheStatus() {
        Map<String, Object> status = new HashMap<>();
        
        boolean exists = contentCacheService.isCacheExists();
        long expiration = contentCacheService.getCacheExpiration();
        
        status.put("exists", exists);
        status.put("expiration_seconds", expiration);
        
        if (expiration > 0) {
            status.put("expiration_hours", expiration / 3600.0);
        }
        
        return ResponseEntity.ok(status);
    }
    
    /**
     * 刷新职位缓存
     */
    @PostMapping("/positions/refresh")
    public ResponseEntity<Map<String, String>> refreshPositionCache() {
        log.info("Admin manually refreshing position cache");
        contentCacheService.refreshActivePositionsCache();
        
        Map<String, String> response = new HashMap<>();
        response.put("message", "Position cache refreshed successfully");
        response.put("status", "success");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 清除职位缓存
     */
    @DeleteMapping("/positions")
    public ResponseEntity<Map<String, String>> clearPositionCache() {
        log.info("Admin manually clearing position cache");
        contentCacheService.clearActivePositionsCache();
        
        Map<String, String> response = new HashMap<>();
        response.put("message", "Position cache cleared successfully");
        response.put("status", "success");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取所有缓存状态概览
     */
    @GetMapping("/overview")
    public ResponseEntity<Map<String, Object>> getCacheOverview() {
        Map<String, Object> overview = new HashMap<>();
        
        // 职位缓存状态
        Map<String, Object> positionStatus = new HashMap<>();
        positionStatus.put("exists", contentCacheService.isCacheExists());
        positionStatus.put("expiration_seconds", contentCacheService.getCacheExpiration());
        
        overview.put("positions", positionStatus);
        
        // 可以在这里添加其他缓存的状态
        
        return ResponseEntity.ok(overview);
    }
}