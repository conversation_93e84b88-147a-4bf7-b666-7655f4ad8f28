package com.redteamobile.talentsphere.controller;

import com.redteamobile.talentsphere.dto.PublicPositionDTO;
import com.redteamobile.talentsphere.dto.PublicPositionsResponse;
import com.redteamobile.talentsphere.dto.PublicResumeSubmissionDTO;
import com.redteamobile.talentsphere.dto.ResumeSubmissionResponse;
import com.redteamobile.talentsphere.dto.PublicJobDetailDTO;
import com.redteamobile.talentsphere.dto.PublicJobsResponse;

import com.redteamobile.talentsphere.model.Position;
import com.redteamobile.talentsphere.model.Resume;
import com.redteamobile.talentsphere.model.ResumeDocument;
import com.redteamobile.talentsphere.service.PositionService;
import com.redteamobile.talentsphere.service.ContentCacheService;
import com.redteamobile.talentsphere.service.ResumeService;
import com.redteamobile.talentsphere.service.ResumeStorageService;
import com.redteamobile.talentsphere.service.SecurityAuditService;
import com.redteamobile.talentsphere.component.IpRateLimiter;
import com.redteamobile.talentsphere.util.RequestUtils;
import com.redteamobile.talentsphere.util.FileCheckUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletRequest;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/public")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class PublicContentController {

    private final PositionService positionService;
    private final ContentCacheService contentCacheService;
    private final ResumeService resumeService;
    private final ResumeStorageService resumeStorageService;
    private final IpRateLimiter ipRateLimiter;
    private final SecurityAuditService securityAuditService;

    /**
     * Get all active positions for public resume submission
     * @return List of active positions
     */
    @GetMapping("/positions")
    public ResponseEntity<PublicPositionsResponse> getActivePositions(HttpServletRequest request) {
        String clientIp = RequestUtils.getClientIpAddress(request);
        String userAgent = RequestUtils.getUserAgent(request);
        
        // 检查频率限制
        if (!ipRateLimiter.isAllowed(clientIp)) {
            securityAuditService.logRateLimitExceeded(clientIp, userAgent, "/api/public/positions", "General rate limit");
            PublicPositionsResponse response = new PublicPositionsResponse(false, null, "Too many requests. Please try again later.");
            return ResponseEntity.status(429).body(response);
        }
        try {
            List<Position> activePositions = contentCacheService.getActivePositions();
            List<PublicPositionDTO> publicPositions = activePositions.stream()
                    .map(position -> new PublicPositionDTO(
                            position.getId(),
                            position.getTitle(),
                            position.getHireSourceType()
                    ))
                    .collect(Collectors.toList());

            PublicPositionsResponse response = new PublicPositionsResponse(true, publicPositions, null);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error fetching active positions: ", e);
            PublicPositionsResponse errorResponse = new PublicPositionsResponse(false, null, "Failed to fetch positions");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Get all active job details for public job listing
     * @return List of active job details with full information
     */
    @GetMapping("/jobs")
    public ResponseEntity<PublicJobsResponse> getActiveJobDetails(HttpServletRequest request) {
        String clientIp = RequestUtils.getClientIpAddress(request);
        String userAgent = RequestUtils.getUserAgent(request);
        
        // 检查频率限制
        if (!ipRateLimiter.isAllowed(clientIp)) {
            securityAuditService.logRateLimitExceeded(clientIp, userAgent, "/api/public/jobs", "General rate limit");
            PublicJobsResponse response = new PublicJobsResponse(false, null, "Too many requests. Please try again later.");
            return ResponseEntity.status(429).body(response);
        }
        
        try {
            List<Position> activePositions = contentCacheService.getActivePositions();
            List<PublicJobDetailDTO> publicJobs = activePositions.stream()
                    .map(position -> new PublicJobDetailDTO(
                            position.getId(),
                            position.getTitle(),
                            position.getLevel(),
                            position.getWorkplace(),
                            position.getDescription(),
                            position.getRequirements(),
                            position.getHireSourceType(),
                            position.getUpdatedAt()
                    ))
                    .collect(Collectors.toList());

            PublicJobsResponse response = new PublicJobsResponse(true, publicJobs, null);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error fetching active job details: ", e);
            PublicJobsResponse errorResponse = new PublicJobsResponse(false, null, "Failed to fetch job details");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }



    /**
     * Submit a resume for a position
     * @param submissionDto Resume submission data
     * @return Submission result
     */
    @PostMapping("/submit-resume")
    public ResponseEntity<ResumeSubmissionResponse> submitResume(@ModelAttribute PublicResumeSubmissionDTO submissionDto,
                                                               HttpServletRequest request) {
        String clientIp = RequestUtils.getClientIpAddress(request);
        String userAgent = RequestUtils.getUserAgent(request);
        
        // 检查简历提交频率限制（更严格）
        if (!ipRateLimiter.isSubmissionAllowed(clientIp)) {
            securityAuditService.logRateLimitExceeded(clientIp, userAgent, "/api/public/submit-resume", "Submission rate limit");
            ResumeSubmissionResponse response = new ResumeSubmissionResponse(false, "Too many submission attempts. Please try again later.");
            return ResponseEntity.status(429).body(response);
        }
        try {
            // 基本文件验证
            String filename = submissionDto.getResume().getOriginalFilename();
            String contentType = submissionDto.getResume().getContentType();
            long fileSize = submissionDto.getResume().getSize();
            
            // 文件安全检查
            boolean securityCheckPassed = FileCheckUtils.performFileSecurityCheck(clientIp, filename, contentType, fileSize, securityAuditService);
            securityAuditService.logFileUploadSecurity(clientIp, filename, contentType, fileSize, securityCheckPassed);
            
            if (!securityCheckPassed) {
                ResumeSubmissionResponse response = new ResumeSubmissionResponse(false, "File security check failed");
                return ResponseEntity.badRequest().body(response);
            }

            // Check if privacy policy is accepted
            if (!Boolean.TRUE.equals(submissionDto.getPrivacyAccepted())) {
                ResumeSubmissionResponse response = new ResumeSubmissionResponse(false, "Privacy policy must be accepted");
                return ResponseEntity.badRequest().body(response);
            }

            // Get position and its interviewers for screeners
            Position position = positionService.getPosition(submissionDto.getPositionId());
            if (position == null || !position.getIsActive()) {
                ResumeSubmissionResponse response = new ResumeSubmissionResponse(false, "Invalid or inactive position selected");
                return ResponseEntity.badRequest().body(response);
            }

            // Use existing importResume method which handles everything
            Resume savedResume = resumeService.importResume(
                    submissionDto.getResume(),
                    "Website",
                    submissionDto.getPositionId(),
                    position.getInterviewers()
            );

            // Get the stored document for comparison
            ResumeDocument document = null; // Will be created by importResume

            // Update resume with parsed information comparison
            updateResumeWithUserInfo(savedResume, submissionDto, document);

            // 记录成功的简历提交
            securityAuditService.logPublicResumeSubmission(clientIp, userAgent, 
                    submissionDto.getFullName(), submissionDto.getEmail(), submissionDto.getPositionId());
            
            log.info("Public resume submitted successfully. Resume ID: {}, Candidate: {}", 
                    savedResume.getId(), submissionDto.getFullName());

            ResumeSubmissionResponse response = new ResumeSubmissionResponse(
                true, 
                "Resume submitted successfully. Thank you for your application!", 
                savedResume.getId()
            );
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error submitting resume: ", e);
            ResumeSubmissionResponse response = new ResumeSubmissionResponse(false, "Failed to submit resume. Please try again later.");
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Update resume with user-provided information, giving priority to user input
     * @param resume The saved resume
     * @param submissionDto User submission data
     * @param document Resume document with parsed data
     */
    private void updateResumeWithUserInfo(Resume resume, PublicResumeSubmissionDTO submissionDto, ResumeDocument document) {
        try {
            boolean needsUpdate = false;
            
            // Compare and update candidate name
            String userProvidedName = submissionDto.getFullName();
            String candidateName = resume.getCandidateName();
            if (!userProvidedName.equals(candidateName)) {
                resume.setCandidateName(userProvidedName);
                needsUpdate = true;
                log.info("Updated candidate name from '{}' to user-provided '{}'", candidateName, userProvidedName);
            }

            // Compare and update email
            String userProvidedEmail = submissionDto.getEmail();
            String candidateEmail = resume.getEmail();
            if (!userProvidedEmail.equals(candidateEmail)) {
                resume.setEmail(userProvidedEmail);
                needsUpdate = true;
                log.info("Updated email from '{}' to user-provided '{}'", candidateEmail, userProvidedEmail);
            }

            // Compare and update phone
            String userProvidedPhone = submissionDto.getFullPhoneNumber();
            String candidatePhone = resume.getPhone();
            if (!userProvidedPhone.equals(candidatePhone)) {
                resume.setPhone(userProvidedPhone);
                needsUpdate = true;
                log.info("Updated phone from '{}' to user-provided '{}'", candidatePhone, userProvidedPhone);
            }

            // Save updates if any changes were made
            if (needsUpdate) {
                resumeService.updateResume(resume, "Website");
                log.info("Resume information updated with user-provided data for resume ID: {}", resume.getId());
            }
            
        } catch (Exception e) {
            log.error("Error updating resume with user information: ", e);
        }
    }
}