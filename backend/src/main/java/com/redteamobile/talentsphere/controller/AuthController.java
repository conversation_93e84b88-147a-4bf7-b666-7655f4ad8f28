package com.redteamobile.talentsphere.controller;

import com.redteamobile.talentsphere.dto.LoginRequest;
import com.redteamobile.talentsphere.dto.LoginResponse;
import com.redteamobile.talentsphere.dto.SignupRequest;
import com.redteamobile.talentsphere.dto.SignupResponse;
import com.redteamobile.talentsphere.dto.ForgotPasswordRequest;
import com.redteamobile.talentsphere.dto.VerifyResetCodeRequest;
import com.redteamobile.talentsphere.dto.ResetPasswordRequest;
import com.redteamobile.talentsphere.service.AuthService;
import com.redteamobile.talentsphere.util.RSAUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @Autowired
    private AuthService authService;
    @Autowired
    private RSAUtils rsaUtils;

    @PostMapping("/login")
    public ResponseEntity<LoginResponse> login(@RequestBody LoginRequest request) {
        String decryptedPassword = rsaUtils.decrypt(request.getEncodedPassword());
        return ResponseEntity.ok(authService.login(request.getEmail(), decryptedPassword));
    }

    @PostMapping("/signup")
    public ResponseEntity<SignupResponse> signup(@RequestBody SignupRequest request) {
        String decryptedPassword = rsaUtils.decrypt(request.getEncodedPassword());
        SignupResponse signupResponse = authService.signup(request.getEmail(),
                request.getUsername(),
                decryptedPassword);
        return ResponseEntity.ok(signupResponse);
    }

    @PostMapping("/logout")
    public ResponseEntity<Void> logout() {
        SecurityContextHolder.clearContext();
        return ResponseEntity.ok().build();
    }

    @PostMapping("/forgot-password")
    public ResponseEntity<Void> forgotPassword(@RequestBody ForgotPasswordRequest request) {
        authService.sendResetCode(request.getEmail());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/verify-reset-code")
    public ResponseEntity<Boolean> verifyResetCode(@RequestBody VerifyResetCodeRequest request) {
        boolean isValid = authService.verifyResetCode(request.getEmail(), request.getCode());
        return ResponseEntity.ok(isValid);
    }

    @PostMapping("/reset-password")
    public ResponseEntity<Void> resetPassword(@RequestBody ResetPasswordRequest request) {
        String decryptedPassword = rsaUtils.decrypt(request.getEncodedPassword());
        authService.resetPassword(request.getEmail(), request.getCode(), decryptedPassword);
        return ResponseEntity.ok().build();
    }
}

