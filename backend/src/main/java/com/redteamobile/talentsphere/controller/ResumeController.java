package com.redteamobile.talentsphere.controller;


import com.redteamobile.talentsphere.dto.PageResponse;
import com.redteamobile.talentsphere.dto.ResumeUpdateRequest;
import com.redteamobile.talentsphere.dto.TalentPoolTransferRequest;
import com.redteamobile.talentsphere.exception.ErrorResponse;
import com.redteamobile.talentsphere.model.Position;
import com.redteamobile.talentsphere.model.Resume;
import com.redteamobile.talentsphere.model.ResumeDocument;
import com.redteamobile.talentsphere.model.User;
import com.redteamobile.talentsphere.service.PositionService;
import com.redteamobile.talentsphere.service.ResumePositionService;
import com.redteamobile.talentsphere.service.ResumeService;
import com.redteamobile.talentsphere.service.ResumeStorageService;
import com.redteamobile.talentsphere.service.DingTalkService;
import com.redteamobile.talentsphere.util.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * Controller for managing resume operations
 */
@RestController
@RequestMapping("/api/resumes")
@Slf4j
public class ResumeController {

    @Autowired
    private ResumeService resumeService;
    @Autowired
    private ResumePositionService resumePositionService;
    @Autowired
    private ResumeStorageService resumeStorageService;
    @Autowired
    private DingTalkService dingTalkService;
    @Autowired
    private PositionService positionService;

    /**
     * Import multiple resumes into the system
     *
     * @param files The resume files (supports PDF, Word, Excel)
     * @param source The platform where the resumes were sourced from
     * @param positionId Position ID these resumes are applying for
     * @param screeners List of screeners these resumes are applying for
     * @return List of created Resume objects with generated IDs
     */
    @PostMapping("/import")
    public ResponseEntity<List<Resume>> importResume(
            @RequestParam("files") List<MultipartFile> files,
            @RequestParam("source") String source,
            @RequestParam("positionId") Long positionId,
            @RequestParam("screeners") List<String> screeners) {
        List<Resume> importedResumes = files.stream()
                .map(file -> resumeService.importResume(file, source, positionId, screeners))
                .toList();
        
        // Send DingTalk notifications to screeners after batch import
        if (!importedResumes.isEmpty()) {
            sendBatchDingTalkNotifications(screeners, importedResumes.size(), positionId);
        }
        
        return ResponseEntity.ok(importedResumes);
    }

    /**
     * Retrieve a specific resume by its ID
     *
     * @param id The unique identifier of the resume
     * @return The Resume object if found
     */
    @GetMapping("/{id}")
    public ResponseEntity<Resume> getResume(@PathVariable Long id) {
        Resume resume = resumeService.getResume(id);
        return ResponseEntity.ok(resume);
    }

    @GetMapping
    public ResponseEntity<PageResponse<Resume>> getResumes(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) List<String> status,
            @RequestParam(required = false) List<Long> positionId) {
        return ResponseEntity.ok(resumeService.getAllResumes(page, pageSize, search, status, positionId));
    }

    /**
     * Get all resumes with a specific status or multiple statuses
     *
     * @param status The status to filter by (e.g., PENDING, SCREENING_PASSED, REJECTED)
     *               Can be a single status or comma-separated multiple statuses
     * @return List of resumes matching the status(es)
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<PageResponse<Resume>> getResumesByStatus(
            @PathVariable String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String search) {
        
        // Check if status contains comma-separated values
        if (status.contains(",")) {
            // Multiple statuses - use the general API with status list
            List<String> statusList = List.of(status.split(","));
            return ResponseEntity.ok(resumeService.getAllResumes(page, pageSize, search, statusList, null));
        } else {
            // Single status - use the existing method
            return ResponseEntity.ok(resumeService.getResumesByStatus(status, page, pageSize, search));
        }
    }

    @GetMapping("/position/{positionId}")
    public ResponseEntity<PageResponse<Resume>> getResumesByPosition(
            @PathVariable Long positionId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String search) {
        return ResponseEntity.ok(resumeService.getResumesByPosition(positionId, page, pageSize, search));
    }

    /**
     * Update the status of a resume
     *
     * @param id The resume ID to update
     * @param status The new status to set
     * @return Empty response with OK status
     */
    @PutMapping("/{id}/status")
    public ResponseEntity<Void> updateStatus(
            @PathVariable Long id,
            @RequestParam String status) {
        resumeService.updateResumeStatus(id, status);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteResume(@PathVariable Long id) {
        resumeService.deleteResume(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{id}/document")
    public ResponseEntity<?> getDocument(@PathVariable Long id, @RequestHeader(value = "Accept", required = false) String acceptHeader) {
        Resume resume = resumeService.getResume(id);
        if (resume == null) {
            return ResponseEntity
                    .status(HttpStatus.NOT_FOUND)
                    .body(new ErrorResponse("Resume not found", "No resume found with ID: " + id));
        }
        ResumeDocument document = resumeStorageService.getByResumeId(resume.getUuid());
        if (document == null) {
            return ResponseEntity
                    .status(HttpStatus.NOT_FOUND)
                    .body(new ErrorResponse("Document not found", "No document found for resume with ID: " + id));
        }
        List<Position> positions = positionService.getPositionsByResumeId(id);
        if (positions.isEmpty()) {
            return ResponseEntity
                    .status(HttpStatus.NOT_FOUND)
                    .body(new ErrorResponse("Position not found", "No positions associated with resume ID: " + id));
        }
        Position position = positions.get(0);

        String candidateName = resume.getCandidateName();
        String fileExtension = document.getFileName().substring(document.getFileName().lastIndexOf('.'));
        String fileName = "【" + position.getTitle() + "_" + position.getWorkplace() + "】" + candidateName + "_" + DateTimeUtils.current() + fileExtension;
        // 对文件名进行 URL 编码
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8)
                .replace("+", "%20"); // 空格在 URL 编码中会变成 +，但在 Content-Disposition 中应该是 %20

        // 如果请求头包含 application/octet-stream，返回二进制文件
        if (acceptHeader != null && acceptHeader.contains(MediaType.APPLICATION_OCTET_STREAM_VALUE)) {
            // 为不同浏览器提供不同的文件名格式
            String asciiFileName = "resume" + fileExtension; // 简单的 ASCII 文件名作为后备

            // 修改 Content-Disposition 头的格式，确保完全符合标准
            String contentDisposition = String.format(
                    "attachment; filename=\"%s\"; filename*=UTF-8''%s",
                    asciiFileName,
                    encodedFileName
            );

            // 获取Base64解码后的字节数组
            byte[] fileContent = resumeStorageService.getResumeContentBytes(document);

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .header("Content-Disposition", contentDisposition)
                    // 添加自定义响应头作为备用方案
                    .header("X-Filename", encodedFileName)
                    .body(fileContent);
        }

        // 否则返回 JSON 格式的文档信息（包含二进制内容）
        // 对于GridFS存储的文件，需要从GridFS读取内容并编码为Base64
        if (document.isStoredInGridFS()) {
            byte[] fileContent = resumeStorageService.getResumeContentBytes(document);
            String base64Content = Base64.getEncoder().encodeToString(fileContent);
            // 创建一个新的文档对象，包含Base64编码的内容
            ResumeDocument responseDocument = new ResumeDocument();
            responseDocument.setId(document.getId());
            responseDocument.setResumeId(document.getResumeId());
            responseDocument.setFileName(document.getFileName());
            responseDocument.setContentType(document.getContentType());
            responseDocument.setContent(base64Content); // 设置Base64编码的内容
            responseDocument.setSize(document.getSize());
            responseDocument.setUploadDate(document.getUploadDate());
            responseDocument.setExtractedText(document.getExtractedText());
            responseDocument.setExtractedSkills(document.getExtractedSkills());
            responseDocument.setUploadedBy(document.getUploadedBy());
            return ResponseEntity.ok(responseDocument);
        } else {
            // 对于Base64存储的文件，直接返回
            return ResponseEntity.ok(document);
        }
    }

    @PatchMapping("/{id}")
    public ResponseEntity<?> updateResume(@PathVariable Long id,
                                          @RequestBody ResumeUpdateRequest request) {
        Resume resume = resumeService.getResume(id);
        if (resume == null) {
            return ResponseEntity
                    .status(HttpStatus.NOT_FOUND)
                    .body(new ErrorResponse("Resume not found", "No resume found with ID: " + id));
        }

        resume.setCandidateName(request.getCandidateName());
        resume.setEmail(request.getEmail());
        resume.setPhone(request.getPhone());

        int result = resumeService.updateResume(resume, "Manual");
        if (result == 0) {
            return ResponseEntity
                    .status(HttpStatus.NOT_FOUND)
                    .body(new ErrorResponse("Update failed", "Failed to update resume with ID: " + id));
        }

        // 如果提供了职位和筛选者信息，更新简历职位关联
        if (request.getPositionId() != null) {
            resumePositionService.updateResumePosition(id, request.getPositionId(), "APPLIED", request.getScreeners());
        }

        return ResponseEntity.ok(resume);
    }
    
    /**
     * Move resume to talent pool
     * 
     * @param id The resume ID to move to talent pool
     * @return Empty response with OK status
     */
    @PutMapping("/{id}/talent-pool")
    public ResponseEntity<Map<String, String>> moveToTalentPool(@PathVariable Long id) {
        try {
            resumeService.moveToTalentPool(id);
            Map<String, String> response = new HashMap<>();
            response.put("message", "简历已成功移入人才库");
            return ResponseEntity.ok(response);
        } catch (IllegalStateException e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }
    
    /**
     * Get talent pool resumes
     * 
     * @param page Page number
     * @param pageSize Page size
     * @param search Search keyword
     * @return Paginated list of talent pool resumes
     */
    @GetMapping("/talent-pool")
    public ResponseEntity<PageResponse<Resume>> getTalentPoolResumes(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String search) {
        return ResponseEntity.ok(resumeService.getTalentPoolResumes(page, pageSize, search));
    }
    
    /**
     * Move resume from talent pool back to normal screening
     * 
     * @param id The resume ID to move from talent pool
     * @param request Transfer request with position and screeners
     * @return Empty response with OK status
     */
    @PutMapping("/{id}/move-from-talent-pool")
    public ResponseEntity<Map<String, String>> moveFromTalentPool(
            @PathVariable Long id,
            @RequestBody TalentPoolTransferRequest request) {
        resumeService.moveFromTalentPool(id, request.getPositionId(), request.getScreeners());
        Map<String, String> response = new HashMap<>();
        response.put("message", "简历已成功转入筛选流程");
        return ResponseEntity.ok(response);
    }
    
    /**
     * Send DingTalk notifications to screeners after batch resume import
     * @param screeners List of screeners to notify
     * @param resumeCount Number of resumes imported
     * @param positionId Position ID for the resumes
     */
    private void sendBatchDingTalkNotifications(List<String> screeners, int resumeCount, Long positionId) {
        try {
            String currentUserEmail = getCurrentUserEmail();
            String positionName = positionService.getPosition(positionId).getTitle();
            
            for (String screener : screeners) {
                if (!screener.equals(currentUserEmail)) {
                    dingTalkService.sendResumeScreeningNotification(
                        screener,
                        resumeCount,
                        positionName
                    );
                }
            }
        } catch (Exception e) {
            log.error("Failed to send DingTalk notifications to screeners", e);
        }
    }
    
    /**
     * Get current user email from security context
     * @return Current user email
     */
    private String getCurrentUserEmail() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof User user) {
                return user.getEmail();
            }
            return "<EMAIL>";
        } catch (Exception e) {
            return "<EMAIL>";
        }
    }
}