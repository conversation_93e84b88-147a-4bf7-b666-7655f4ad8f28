package com.redteamobile.talentsphere.controller;

import com.redteamobile.talentsphere.dto.PageResponse;
import com.redteamobile.talentsphere.model.InternalNotification;
import com.redteamobile.talentsphere.model.User;
import com.redteamobile.talentsphere.service.NotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/notifications")
public class NotificationController {

    @Autowired
    private NotificationService notificationService;

    @GetMapping
    public ResponseEntity<PageResponse<InternalNotification>> getNotifications(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize) {
        User currentUser = getCurrentUser();
        return ResponseEntity.ok(notificationService.getNotifications(currentUser.getEmail(), page, pageSize));
    }

    @GetMapping("/unread")
    public ResponseEntity<PageResponse<InternalNotification>> getUnreadNotifications(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize) {
        User currentUser = getCurrentUser();
        return ResponseEntity.ok(notificationService.getUnreadNotifications(currentUser.getEmail(), page, pageSize));
    }

    @GetMapping("/unread/count")
    public ResponseEntity<Integer> getUnreadCount() {
        User currentUser = getCurrentUser();
        return ResponseEntity.ok(notificationService.getUnreadCount(currentUser.getEmail()));
    }

    @PatchMapping("/{id}/read")
    public ResponseEntity<Void> markAsRead(@PathVariable Long id) {
        notificationService.markAsRead(id);
        return ResponseEntity.ok().build();
    }

    @PatchMapping("/read-all")
    public ResponseEntity<Void> markAllAsRead() {
        User currentUser = getCurrentUser();
        notificationService.markAllAsRead(currentUser.getEmail());
        return ResponseEntity.ok().build();
    }

    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof User) {
            return (User) authentication.getPrincipal();
        }
        throw new RuntimeException("User not authenticated");
    }
} 