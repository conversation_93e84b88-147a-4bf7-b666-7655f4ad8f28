package com.redteamobile.talentsphere.enums;

import lombok.Getter;

/**
 * DingTalk通知类型枚举
 */
@Getter
public enum DingTalkNotificationType {
    
    RESUME_SCREENING("📋", "#1890FF", "简历筛选提醒", "请及时登录系统进行简历筛选。", "/dashboard/resumes?status=new"),
    RESUME_SHORTLISTED_BATCH("🔎", "#52C41A", "简历筛选完成通知", "有新的简历已通过筛选，可以安排面试了。", "/dashboard/resumes?status=shortlisted&positionId=%d&timestamp=%d"),
    INTERVIEW_SCHEDULED("📅", "#1890FF", "面试安排通知", "请准时参加面试。", "/dashboard/interviews?interviewId=%d"),
    INTERVIEW_UPDATED("🔄", "#FFA500", "面试变更通知", "面试安排已更新，请查看最新信息。", "/dashboard/interviews?interviewId=%d"),
    INTERVIEW_FEEDBACK("✅", "#52C41A", "面试反馈完成", "面试反馈已提交，请查看评估结果。", "/dashboard/interviews?interviewId=%d"),
    INTERVIEW_CANCELLED("❌", "#FF4D4F", "面试取消通知", "面试已被取消，如有疑问请联系相关人员。", "/dashboard/interviews?interviewId=%d");
    
    private final String icon;
    private final String color;
    private final String title;
    private final String actionText;
    private final String urlTemplate;
    
    DingTalkNotificationType(String icon, String color, String title, String actionText, String urlTemplate) {
        this.icon = icon;
        this.color = color;
        this.title = title;
        this.actionText = actionText;
        this.urlTemplate = urlTemplate;
    }
    
    /**
     * 获取完整的标题（带图标和颜色）
     */
    public String getFormattedTitle() {
        return String.format("%s <font color=\"%s\">您有一个%s来自TalentSphere</font>", icon, color, title);
    }
}