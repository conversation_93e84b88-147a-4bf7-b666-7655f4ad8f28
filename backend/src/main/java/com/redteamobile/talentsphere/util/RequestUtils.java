package com.redteamobile.talentsphere.util;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RequestUtils {
    
    private static final String[] IP_HEADERS = {
        "X-Forwarded-For",
        "X-Real-IP", 
        "Proxy-Client-IP",
        "WL-Proxy-Client-IP",
        "HTTP_CLIENT_IP",
        "HTTP_X_FORWARDED_FOR"
    };
    
    /**
     * 获取客户端真实IP地址
     */
    public static String getClientIpAddress(HttpServletRequest request) {
        String ip = null;
        
        // 首先检查各种代理头
        for (String header : IP_HEADERS) {
            ip = request.getHeader(header);
            if (isValidIp(ip)) {
                // 如果有多个IP，取第一个
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                log.debug("Found client IP {} from header {}", ip, header);
                return ip;
            }
        }
        
        // 如果代理头中没有找到，使用远程地址
        ip = request.getRemoteAddr();
        log.debug("Using remote address as client IP: {}", ip);
        
        return ip != null ? ip : "unknown";
    }
    
    /**
     * 检查IP是否有效
     */
    private static boolean isValidIp(String ip) {
        return ip != null && 
               !ip.isEmpty() && 
               !"unknown".equalsIgnoreCase(ip) && 
               !"0:0:0:0:0:0:0:1".equals(ip) &&
               !"127.0.0.1".equals(ip);
    }
    
    /**
     * 检查是否为内网IP（开发环境使用）
     */
    public static boolean isInternalIp(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        
        return ip.startsWith("192.168.") || 
               ip.startsWith("10.") || 
               ip.startsWith("172.") ||
               "127.0.0.1".equals(ip) ||
               "0:0:0:0:0:0:0:1".equals(ip) ||
               "localhost".equals(ip);
    }
    
    /**
     * 获取User-Agent
     */
    public static String getUserAgent(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        return userAgent != null ? userAgent : "unknown";
    }
}