package com.redteamobile.talentsphere.util;

import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.apache.commons.io.IOUtils;

import javax.crypto.Cipher;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

@Component
public class RSAUtils {
    
    private final PrivateKey privateKey;
    
    public RSAUtils() {
        try {
            this.privateKey = loadPrivateKeyFromPem();
        } catch (Exception e) {
            throw new RuntimeException("Failed to load RSA private key", e);
        }
    }
    
    private PrivateKey loadPrivateKeyFromPem() throws Exception {
        String privateKeyPEM = readPrivateKeyFromFile()
            .replace("-----BEGIN PRIVATE KEY-----", "")
            .replace("-----END PRIVATE KEY-----", "")
            .replaceAll("\\s", "");
            
        byte[] keyBytes = Base64.getDecoder().decode(privateKeyPEM);
        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory kf = KeyFactory.getInstance("RSA");
        return kf.generatePrivate(spec);
    }
    
    private String readPrivateKeyFromFile() throws IOException {
        ClassPathResource resource = new ClassPathResource("pem/rsa_private.pem");
        return IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8);
    }
    
    public String decrypt(String encryptedPassword) {
        try {
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedPassword));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("Failed to decrypt password", e);
        }
    }
} 