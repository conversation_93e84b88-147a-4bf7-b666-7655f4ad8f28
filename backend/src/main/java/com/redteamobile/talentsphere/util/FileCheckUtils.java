package com.redteamobile.talentsphere.util;

import com.redteamobile.talentsphere.service.SecurityAuditService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FileCheckUtils {
    
    /**
     * 执行文件安全检查
     * @param clientIp 客户端IP
     * @param filename 文件名
     * @param contentType 文件MIME类型
     * @param fileSize 文件大小
     * @param securityAuditService 安全审计服务
     * @return 是否通过安全检查
     */
    public static boolean performFileSecurityCheck(String clientIp, String filename, 
                                                 String contentType, long fileSize,
                                                 SecurityAuditService securityAuditService) {
        
        // 文件大小检查 (16MB限制)
        if (fileSize > 16 * 1024 * 1024) {
            securityAuditService.logMaliciousRequest(clientIp, "File upload", "/api/public/submit-resume", 
                    "File size exceeds limit: " + fileSize);
            return false;
        }
        
        // 文件类型检查 - 只允许PDF
        if (contentType == null || !contentType.equals("application/pdf")) {
            securityAuditService.logMaliciousRequest(clientIp, "File upload", "/api/public/submit-resume", 
                    "Invalid content type: " + contentType);
            return false;
        }
        
        // 文件名安全检查
        if (filename == null || filename.trim().isEmpty()) {
            securityAuditService.logMaliciousRequest(clientIp, "File upload", "/api/public/submit-resume", 
                    "Empty filename");
            return false;
        }
        
        // 检查文件名中的危险字符
        if (containsDangerousCharacters(filename)) {
            securityAuditService.logMaliciousRequest(clientIp, "File upload", "/api/public/submit-resume", 
                    "Dangerous characters in filename: " + filename);
            return false;
        }
        
        // 检查文件扩展名
        if (!hasValidExtension(filename)) {
            securityAuditService.logMaliciousRequest(clientIp, "File upload", "/api/public/submit-resume", 
                    "Invalid file extension: " + filename);
            return false;
        }
        
        // 文件名长度检查
        if (filename.length() > 255) {
            securityAuditService.logMaliciousRequest(clientIp, "File upload", "/api/public/submit-resume", 
                    "Filename too long: " + filename.length());
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查文件名是否包含危险字符
     * @param filename 文件名
     * @return 是否包含危险字符
     */
    private static boolean containsDangerousCharacters(String filename) {
        String[] dangerousChars = {"..", "/", "\\", "<", ">", "|", ":", "*", "?", "\"", "'", "`", ";", "&"};
        
        for (String dangerousChar : dangerousChars) {
            if (filename.contains(dangerousChar)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查文件扩展名是否有效
     * @param filename 文件名
     * @return 是否为有效的PDF扩展名
     */
    private static boolean hasValidExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return false;
        }
        
        String lowerCaseFilename = filename.toLowerCase();
        return lowerCaseFilename.endsWith(".pdf");
    }
    
    /**
     * 文件安全检查（不需要审计服务的简化版本）
     * @param filename 文件名
     * @param contentType 文件MIME类型
     * @param fileSize 文件大小
     * @return 是否通过安全检查
     */
    public static boolean isFileSafe(String filename, String contentType, long fileSize) {
        // 文件大小检查
        if (fileSize > 16 * 1024 * 1024) {
            log.warn("File size exceeds limit: {} bytes", fileSize);
            return false;
        }
        
        // 文件类型检查
        if (contentType == null || !contentType.equals("application/pdf")) {
            log.warn("Invalid content type: {}", contentType);
            return false;
        }
        
        // 文件名基本检查
        if (filename == null || filename.trim().isEmpty()) {
            log.warn("Empty filename");
            return false;
        }
        
        // 危险字符检查
        if (containsDangerousCharacters(filename)) {
            log.warn("Dangerous characters in filename: {}", filename);
            return false;
        }
        
        // 扩展名检查
        if (!hasValidExtension(filename)) {
            log.warn("Invalid file extension: {}", filename);
            return false;
        }
        
        // 文件名长度检查
        if (filename.length() > 255) {
            log.warn("Filename too long: {} characters", filename.length());
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取安全的文件名（清理危险字符）
     * @param originalFilename 原始文件名
     * @return 清理后的安全文件名
     */
    public static String getSafeFilename(String originalFilename) {
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            return "unknown.pdf";
        }
        
        String safeFilename = originalFilename;
        
        // 移除危险字符
        safeFilename = safeFilename.replaceAll("[.]{2,}", ".");  // 多个点替换为单个点
        safeFilename = safeFilename.replaceAll("[/\\\\<>|:*?\"';`&]", "_");  // 危险字符替换为下划线
        
        // 限制长度
        if (safeFilename.length() > 255) {
            String nameWithoutExtension = safeFilename.substring(0, safeFilename.lastIndexOf('.'));
            String extension = safeFilename.substring(safeFilename.lastIndexOf('.'));
            int maxNameLength = 255 - extension.length();
            safeFilename = nameWithoutExtension.substring(0, maxNameLength) + extension;
        }
        
        return safeFilename;
    }
}