<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Code</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #374151;
            margin: 0;
            padding: 20px;
            background-color: #f3f4f6;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 32px;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .code {
            font-size: 32px;
            font-weight: bold;
            color: #111827;
            text-align: center;
            padding: 24px;
            background-color: #f3f4f6;
            border-radius: 8px;
            margin: 24px 0;
            letter-spacing: 4px;
        }
        .expiry {
            color: #6b7280;
            font-size: 14px;
            text-align: center;
            margin-top: 16px;
        }
        .warning {
            color: #dc2626;
            font-size: 14px;
            margin-top: 24px;
            padding: 12px;
            background-color: #fef2f2;
            border-radius: 6px;
        }
        .copyright {
            color: #9ca3af;
            font-size: 12px;
            text-align: center;
            margin-top: 24px;
            padding-top: 16px;
            border-top: 1px solid #e5e7eb;
        }
        @media (max-width: 480px) {
            .container {
                padding: 20px;
            }
            .code {
                font-size: 28px;
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Password Reset Request</h2>
        
        <p>You have requested to reset your password. Please use the following code to complete the password reset process:</p>
        
        <div class="code" th:text="${resetCode}">123456</div>
        
        <p class="expiry">This code will expire in <span th:text="${expiryMinutes}">10</span> minutes.</p>
        
        <p>If you did not request a password reset, please ignore this email and ensure your account is secure.</p>
        
        <p class="warning">Do not share this code with anyone.</p>
        
        <p>Best regards,<br><br>TalentSphere Team</p>

        <div class="copyright">
            © <span th:text="${#dates.year(#dates.createNow())}">2024</span> RedteaMobile Inc. All rights reserved.
        </div>
    </div>
</body>
</html> 