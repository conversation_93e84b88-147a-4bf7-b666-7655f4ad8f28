CREATE TABLE IF NOT EXISTS resumes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(64),
    candidate_name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    source VARCHAR(100),
    file_type VARCHAR(50),
    file_path VARCHAR(255),
    status VARCHAR(50),
    import_date DATETIME,
    imported_by VARCHAR(100),
    is_duplicate BOOLEAN DEFAULT FALSE,
    tags TEXT,
    last_modified DATETIME,
    talent_pool BOOLEAN DEFAULT FALSE
);

CREATE TABLE IF NOT EXISTS resume_screenings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    resume_id BIGINT,
    education_level VARCHAR(100),
    years_of_experience INT,
    skill_tags TEXT,
    location VARCHAR(255),
    screening_result VARCHAR(50),
    matching_score DECIMAL(5,2),
    screening_notes TEXT,
    screening_date DATETIME,
    screened_by <PERSON><PERSON><PERSON><PERSON>(100),
    <PERSON>OREI<PERSON><PERSON> KEY (resume_id) REFERENCES resumes(id)
);

CREATE TABLE IF NOT EXISTS interviews (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    resume_id BIGINT,
    position_id BIGINT,
    interview_type VARCHAR(50),
    scheduled_time DATETIME,
    interviewer VARCHAR(100),
    candidate VARCHAR(100),
    contact VARCHAR(512),
    cc_list VARCHAR(512),
    status VARCHAR(50),
    result TINYINT,
    remark TEXT,
    location VARCHAR(10) COMMENT '面试方式',
    rating DECIMAL(2,1),
    feedback TEXT,
    qr_code_url VARCHAR(255),
    checkin_time DATETIME,
    updated_at DATETIME,
    FOREIGN KEY (resume_id) REFERENCES resumes(id)
);

CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    role VARCHAR(20) NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    dingtalk_userid VARCHAR(64) DEFAULT NULL COMMENT '钉钉用户ID'
);

CREATE TABLE IF NOT EXISTS positions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    level VARCHAR(50),
    department VARCHAR(100),
    workplace VARCHAR(255),
    requirements TEXT,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    headcount INT DEFAULT 1,
    filled INT DEFAULT 0,
    interviewers TEXT,
    created_at DATETIME,
    updated_at DATETIME
);

CREATE TABLE IF NOT EXISTS resume_positions (
    resume_id BIGINT,
    position_id BIGINT,
    application_date DATETIME,
    status VARCHAR(50),  -- APPLIED, SCREENING, INTERVIEWING, OFFERED, REJECTED
    screeners TEXT,
    PRIMARY KEY (resume_id, position_id),
    FOREIGN KEY (resume_id) REFERENCES resumes(id),
    FOREIGN KEY (position_id) REFERENCES positions(id)
);

CREATE TABLE IF NOT EXISTS resume_operation_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    resume_id BIGINT NOT NULL,
    operation VARCHAR(50) NOT NULL,
    operated_by VARCHAR(255) NOT NULL,
    opt_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    remark TEXT,
    FOREIGN KEY (resume_id) REFERENCES resumes(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS internal_notifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    sender VARCHAR(32),
    receiver VARCHAR(32) NOT NULL,
    status VARCHAR(10) DEFAULT 'CREATED',
    content TEXT NOT NULL,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    reference_type VARCHAR(20),
    reference_id BIGINT,
    INDEX idx_receiver (receiver),
    INDEX idx_status (status),
    INDEX idx_created_time (created_time)
);

CREATE TABLE IF NOT EXISTS staff_info (
    staff_id VARCHAR(32) NOT NULL,
    staff_name VARCHAR(255) NOT NULL,
    gender BOOLEAN NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    start_date DATE NOT NULL,
    employ_status BOOLEAN DEFAULT TRUE,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (email)
);

-- Add indexes for better performance
CREATE INDEX idx_resume_status ON resumes(status);
CREATE INDEX idx_resume_talent_pool ON resumes(talent_pool);
CREATE INDEX idx_resume_status_talent_pool ON resumes(status, talent_pool);
CREATE INDEX idx_interview_resume ON interviews(resume_id);
CREATE INDEX idx_screening_resume ON resume_screenings(resume_id);
CREATE INDEX idx_resume_logs ON resume_operation_logs(resume_id);

-- Create application user and grant privileges
CREATE USER IF NOT EXISTS 'talentsphere_user'@'%' IDENTIFIED BY 'talentsphere_pass';
GRANT ALL PRIVILEGES ON talentsphere.* TO 'talentsphere_user'@'%';
FLUSH PRIVILEGES;