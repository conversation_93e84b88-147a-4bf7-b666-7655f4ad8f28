<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redteamobile.talentsphere.mapper.ResumeMapper">
    
    <resultMap id="ResumeMap" type="com.redteamobile.talentsphere.model.Resume">
        <id property="id" column="id"/>
        <result property="uuid" column="uuid"/>
        <result property="candidateName" column="candidate_name"/>
        <result property="email" column="email"/>
        <result property="phone" column="phone"/>
        <result property="source" column="source"/>
        <result property="fileType" column="file_type"/>
        <result property="filePath" column="file_path"/>
        <result property="status" column="status"/>
        <result property="importDate" column="import_date"/>
        <result property="importedBy" column="imported_by"/>
        <result property="isDuplicate" column="is_duplicate"/>
        <result property="tags" column="tags"/>
        <result property="lastModified" column="last_modified"/>
    </resultMap>

</mapper> 