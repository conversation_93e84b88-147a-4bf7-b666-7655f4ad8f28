<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redteamobile.talentsphere.mapper.InterviewMapper">
    
    <resultMap id="InterviewMap" type="com.redteamobile.talentsphere.model.Interview">
        <id property="id" column="id"/>
        <result property="resumeId" column="resume_id"/>
        <result property="positionId" column="position_id"/>
        <result property="candidate" column="candidate"/>
        <result property="candidateEmail" column="candidate_email"/>
        <result property="interviewer" column="interviewer"/>
        <result property="contact" column="contact"/>
        <result property="ccList" column="cc_list"/>
        <result property="interviewType" column="interview_type"/>
        <result property="scheduledTime" column="scheduled_time"/>
        <result property="status" column="status"/>
        <result property="rating" column="rating"/>
        <result property="feedback" column="feedback"/>
        <result property="qrCodeUrl" column="qr_code_url"/>
        <result property="checkinTime" column="checkin_time"/>
        <result property="result" column="result"/>
        <result property="remark" column="remark"/>
        <result property="location" column="location"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>
</mapper> 