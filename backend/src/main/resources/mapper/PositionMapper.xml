<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redteamobile.talentsphere.mapper.PositionMapper">
    
    <resultMap id="PositionMap" type="com.redteamobile.talentsphere.model.Position">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="level" column="level"/>
        <result property="department" column="department"/>
        <result property="workplace" column="workplace"/>
        <result property="requirements" column="requirements"/>
        <result property="description" column="description"/>
        <result property="isActive" column="is_active"/>
        <result property="headcount" column="headcount"/>
        <result property="filled" column="filled"/>
        <result property="hireSourceType" column="hire_source_type" typeHandler="com.redteamobile.talentsphere.config.HireSourceTypeEnumTypeHandler"/>
        <result property="interviewers" column="interviewers"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

</mapper> 