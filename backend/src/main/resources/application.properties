host.ip=************

# Database Configuration
spring.datasource.url=****************************************
spring.datasource.username=talentsphere_user
spring.datasource.password=talentsphere_pass
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JWT Configuration
jwt.signing.key=hddnHq7B28Jd501uHYgo0GzEDLm13Q7DXfKiZCnJ8STD7a2ETyDBm/erYmvSfwwi7+PujmKahRx6xgSbd9FLEFY5XD98dkVoeIuKdeBl8aNQcrKk0Xct9QAtCFAiy/heyB1rCtRy1zQfuMb8EW2KUoWwoTL7Ie6nzcJPurePOTOEir83WXio1BHDvBQiJfNzORrP/GTmk2iS15KGIIg7EPb/4tUJXk1jDH+HZ0wc90jQaigu2TyjIQdx0/X5ThnnFqpKOYjjrhUvqdz480GS0os6fl0CYTizEM93xN0Uvp1Hobz24C/yocqKaxh//fFfeUStxAcN3Jfm2680gfKICA==
jwt.expiration=86400000

# File Upload Configuration
spring.servlet.multipart.max-file-size=30MB
spring.servlet.multipart.max-request-size=30MB
file.upload-dir=./uploads/resumes

# Server Configuration
server.port=8095

# MongoDB Configuration
spring.data.mongodb.host=localhost
spring.data.mongodb.port=27017
spring.data.mongodb.database=talentsphere
spring.data.mongodb.auto-index-creation=false

# MyBatis Configuration
mybatis.mapper-locations=classpath:mapper/*.xml
mybatis.type-aliases-package=com.redteamobile.talentsphere.mapper
mybatis.configuration.map-underscore-to-camel-case=true

# Custom allowed origins for SecurityConfig
cors.allowed-origins=http://${host.ip}:3095,http://localhost:3095

# Email Configuration
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=rkxjnilfpqcjypaq
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Thymeleaf Configuration
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

app.base-url=http://${host.ip}

# Users belong to HR role
role.hr.users=<EMAIL>,<EMAIL>,<EMAIL>

logging.level.org.springframework.security= DEBUG

# HTML2Canvas Service Configuration
html2canvas.service.url=http://${host.ip}:3102/render

# Redis Configuration
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=
spring.data.redis.database=0
spring.data.redis.timeout=60000
# Redis\u8FDE\u63A5\u6C60\u914D\u7F6E
spring.data.redis.lettuce.pool.max-active=8
spring.data.redis.lettuce.pool.max-idle=8
spring.data.redis.lettuce.pool.min-idle=0
spring.data.redis.lettuce.pool.max-wait=-1ms

# DingTalk Configuration
dingtalk.enabled=true
dingtalk.app-key=ding2kittqa5p7spoiki
dingtalk.app-secret=cTzXKrkWpeWL0VJPnB3zhR8frP6V78CRjYRMwhmK1KSmZcSLIj6qtTxaKljqyWUA
dingtalk.agent-id=3929372175
dingtalk.api.base-url=https://oapi.dingtalk.com
dingtalk.api.new-base-url=https://api.dingtalk.com
dingtalk.api.timeout=10000