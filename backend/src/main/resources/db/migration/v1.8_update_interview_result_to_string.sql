-- 迁移面试结果字段从boolean到varchar，支持PENDING状态
-- V1.9 - Update Interview Result To String

-- 第一步：添加新的result字段
ALTER TABLE interview ADD COLUMN result_new VARCHAR(20) NULL DEFAULT NULL;

-- 第二步：迁移现有数据
UPDATE interview SET result_new = 
  CASE 
    WHEN result IS NULL THEN NULL
    WHEN result = true THEN 'PASS'
    WHEN result = false THEN 'REJECT'
  END;

-- 第三步：删除旧字段
ALTER TABLE interview DROP COLUMN result;

-- 第四步：重命名新字段
ALTER TABLE interview RENAME COLUMN result_new TO result;

-- 第五步：添加约束确保数据完整性
ALTER TABLE interview ADD CONSTRAINT chk_interview_result 
  CHECK (result IS NULL OR result IN ('PASS', 'REJECT', 'PENDING'));
