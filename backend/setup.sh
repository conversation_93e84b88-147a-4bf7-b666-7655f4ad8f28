#!/bin/bash

# Create directory structure
mkdir -p src/main/java/com/redteamobile/talentsphere/{config,controller,dto,mapper,model,service/impl}
mkdir -p src/main/resources/{mapper,templates}
mkdir -p src/test/java/com/redteamobile/talentsphere

# Create basic configuration files if they don't exist
touch src/main/resources/application.properties
touch src/main/resources/schema.sql

# Create basic Java files if they don't exist
cat > src/main/java/com/redteamobile/talentsphere/Application.java << 'EOF'
package com.redteamobile.talentsphere;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
@MapperScan("com.redteamobile.talentsphere.mapper")
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
EOF

# Create basic configuration class
cat > src/main/java/com/redteamobile/talentsphere/config/WebConfig.java << 'EOF'
package com.redteamobile.talentsphere.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/**")
            .allowedOrigins("http://localhost:3000")
            .allowedMethods("GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS")
            .allowedHeaders("*")
            .allowCredentials(true)
            .maxAge(3600);
    }
}
EOF

# Make the script executable
chmod +x setup.sh 